using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Account;
using Platform.Client.Services.Services;
using Platform.Framework.Core;
using System.Text.Json;
using System.Security.Claims;

namespace Platform.Client.Services.Features.Account.SigninForm
{
    public class SigninOfflineFormDataService : ISignInFormDataService
    {
        //private readonly IClientEncryptionService _encryptionService;
        private readonly ILocalStorageService _localStorageService;
        private readonly ISecureKeyManager _secureKeyManager;

        public SigninOfflineFormDataService(ILocalStorageService localStorageService, ISecureKeyManager secureKeyManager)
        { 
            _localStorageService = localStorageService;
            _secureKeyManager = secureKeyManager;
        }

        [SystemClaim(SystemClaimType.SystemDefault)]
        public async Task<string> SaveAsync(SigninFormBusinessObject formBusinessObject)
        {
            try
            {
                // Step 1: Check if user data exists in local storage
                var storedUsername = await _localStorageService.GetValue(ClaimTypes.Name );
                var storedUserId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);

                if (string.IsNullOrEmpty(storedUsername) || string.IsNullOrEmpty(storedUserId))
                {
                    return "Authenticate Online";
                }

                // Step 2: Validate provided username matches stored username
                if (!string.Equals(formBusinessObject.NickName, storedUsername, StringComparison.OrdinalIgnoreCase))
                {
                    return "Authenticate Online";
                }

                // Step 3: Check if encrypted private key exists for this user
                var encryptedPrivateKey = await _localStorageService.GetValue($"encrypted_private_key_{formBusinessObject.NickName}");
                if (string.IsNullOrEmpty(encryptedPrivateKey))
                {
                    return "Authenticate Online";
                }

                // Step 4: Attempt to derive keys and authenticate using SecureKeyManager
                var authenticationSuccess = await _secureKeyManager.DeriveAndStoreKeysAsync(
                    formBusinessObject.NickName!, 
                    formBusinessObject.PassKey!);

                if (!authenticationSuccess)
                {
                    throw new Exception("Invalid credentials");
                }

                // Step 5: Verify RSA key is available in memory
                if (!_secureKeyManager.IsRSAKeyAvailable())
                {
                    throw new Exception("Failed to load cryptographic keys");
                }

                // Step 6: Authentication successful - create AuthorizationClaimsModel
                var authClaims = new AuthorizationClaimsModel(
                    token: GenerateOfflineToken(storedUserId, storedUsername),
                    refreshToken: string.Empty, // No refresh token for offline auth
                    userId: storedUserId,
                    username: storedUsername
                );

                return JsonSerializer.Serialize(authClaims);
            }
            catch (Exception ex)
            {
                // Clear keys on authentication failure
                _secureKeyManager.ClearKeys();
                throw new Exception($"Offline authentication failed: {ex.Message}");
            }
        }

        [SystemClaim(SystemClaimType.SystemDefault)]
        public Task<SigninFormBusinessObject> GetItemByIdAsync(string id)
        {
            throw new NotImplementedException("GetItemByIdAsync not supported for offline authentication");
        }

        private string GenerateOfflineToken(string userId, string username)
        {
            // Generate a simple offline token (not JWT for offline scenarios)
            // This is a placeholder - in production you might want a more sophisticated approach
            var tokenData = new
            {
                UserId = userId,
                Username = username,
                IssuedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                ExpiresAt = DateTimeOffset.UtcNow.AddHours(24).ToUnixTimeSeconds(),
                Type = "offline"
            };

            var tokenJson = JsonSerializer.Serialize(tokenData);
            return Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(tokenJson));
        }
    }
}
