using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Platform.Framework.Core;

namespace Platform.Razor.Features.Navigation.BottomTabs
{
    public partial class BottomTabNavigation : IDisposable
    {
        private bool isAuthenticated = false;
        private bool showTabs = false;
        private string currentRoute = string.Empty;
        private int unreadMessagesCount = 0;
        private int friendRequestsCount = 0; 
        [Inject] private IJSRuntime JSRuntime { get; set; } = null!;
        [Inject] private ILocalStorageService StorageService { get; set; } = null!;
        // Routes where tabs should be hidden
        private readonly string[] hiddenTabRoutes = {
            "/", "/news", "/home",
            "/signin", "/login", "/signup", "/register",
            "/captcha", "/verify", "/forgot-password", "/reset-password",
            "/welcome", "/stealth-demo", "/auth-demo"
        };

        protected override async Task OnInitializedAsync()
        {
            // Subscribe to navigation changes
            Navigation.LocationChanged += OnLocationChanged;
            
            // Check initial authentication state
            await CheckAuthenticationState();
            
            // Update current route
            UpdateCurrentRoute();
            
            // Load notification counts
            await LoadNotificationCounts();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                // Check authentication state again after render
                await CheckAuthenticationState();
                StateHasChanged();
            }
        }

        /// <summary>
        /// Handles navigation location changes
        /// </summary>
        private async void OnLocationChanged(object? sender, Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs e)
        {
            UpdateCurrentRoute();
            await CheckAuthenticationState();
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Updates the current route from navigation
        /// </summary>
        private void UpdateCurrentRoute()
        {
            var uri = new Uri(Navigation.Uri);
            currentRoute = uri.AbsolutePath.ToLowerInvariant();
        }

        /// <summary>
        /// Checks authentication state and determines if tabs should be shown
        /// </summary>
        private async Task CheckAuthenticationState()
        {
            try
            {
                // Check authentication state
                var authState = await AuthStateProvider.GetAuthenticationStateAsync();
                isAuthenticated = authState.User?.Identity?.IsAuthenticated == true;

                // If not authenticated via AuthenticationStateProvider, check storage
                if (!isAuthenticated)
                {
                    var authToken = await StorageService.GetValue("auth_token");
                    isAuthenticated = !string.IsNullOrEmpty(authToken);
                }

                // Determine if tabs should be shown
                showTabs = isAuthenticated && !ShouldHideTabs();
            }
            catch (Exception)
            {
                // If there's an error checking auth state, assume not authenticated
                isAuthenticated = false;
                showTabs = false;
            }
        }

        /// <summary>
        /// Determines if tabs should be hidden based on current route
        /// </summary>
        private bool ShouldHideTabs()
        {
            if (string.IsNullOrEmpty(currentRoute))
                return true;

            // Check exact matches
            if (hiddenTabRoutes.Contains(currentRoute))
                return true;

            // Check pattern matches
            if (currentRoute.StartsWith("/reset-password/"))
                return true;

            return false;
        }

        /// <summary>
        /// Loads notification counts for badges
        /// </summary>
        private async Task LoadNotificationCounts()
        {
            try
            {
                if (!isAuthenticated)
                {
                    unreadMessagesCount = 0;
                    friendRequestsCount = 0;
                    return;
                }

                // Load unread messages count
                var unreadCount = await StorageService.GetValue("unread_messages_count");
                if (int.TryParse(unreadCount, out var unread))
                {
                    unreadMessagesCount = unread;
                }

                // Load friend requests count
                var requestsCount = await StorageService.GetValue("friend_requests_count");
                if (int.TryParse(requestsCount, out var requests))
                {
                    friendRequestsCount = requests;
                }
            }
            catch (Exception)
            {
                // If there's an error loading counts, default to 0
                unreadMessagesCount = 0;
                friendRequestsCount = 0;
            }
        }

        /// <summary>
        /// Navigates to the specified tab route
        /// </summary>
        private async Task NavigateToTab(string route)
        {
            try
            {
                // Store the selected tab for persistence
                await StorageService.SetValue(route, "selected_tab");
                
                // Navigate to the route
                Navigation.NavigateTo(route, replace: false);
                
                // Update current route immediately
                currentRoute = route.ToLowerInvariant();
                
                // Trigger haptic feedback on mobile devices
                await TriggerHapticFeedback();
            }
            catch (Exception)
            {
                // If navigation fails, just navigate without storage
                Navigation.NavigateTo(route, replace: false);
            }
        }

        /// <summary>
        /// Triggers haptic feedback for mobile devices
        /// </summary>
        private async Task TriggerHapticFeedback()
        {
            try
            {
                await JSRuntime.InvokeVoidAsync("eval", @"
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10);
                    }
                ");
            }
            catch
            {
                // Haptic feedback is optional, ignore errors
            }
        }

        /// <summary>
        /// Checks if the given route is the active tab
        /// </summary>
        private bool IsActiveTab(string route)
        {
            if (string.IsNullOrEmpty(currentRoute) || string.IsNullOrEmpty(route))
                return false;

            // Handle special cases for messages tab
            if (route == "/chat")
            {
                return currentRoute == "/chat" || currentRoute == "/messages" || 
                       currentRoute.StartsWith("/chat/") || currentRoute.StartsWith("/messages/");
            }

            // Handle other tabs
            return currentRoute == route.ToLowerInvariant() || 
                   currentRoute.StartsWith(route.ToLowerInvariant() + "/");
        }

        /// <summary>
        /// Gets CSS classes for tab buttons with WhatsApp minimalistic theme
        /// </summary>
        private string GetTabClasses(string route)
        {
            var baseClasses = "relative";

            if (IsActiveTab(route))
            {
                return $"{baseClasses} text-nothing-black-800 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20";
            }

            return $"{baseClasses} text-nothing-black-600 dark:text-nothing-black-400 hover:text-nothing-black-700 dark:hover:text-nothing-black-200 hover:bg-nothing-black-100 dark:hover:bg-nothing-black-800";
        }

        /// <summary>
        /// Gets CSS classes for tab icons with WhatsApp minimalistic theme
        /// </summary>
        private string GetIconClasses(string route)
        {
            if (IsActiveTab(route))
            {
                return "text-primary-800 dark:text-primary-400";
            }

            return "text-nothing-black-500 dark:text-nothing-black-400";
        }

        /// <summary>
        /// Gets CSS classes for tab labels with WhatsApp minimalistic theme
        /// </summary>
        private string GetLabelClasses(string route)
        {
            if (IsActiveTab(route))
            {
                return "text-primary-800 dark:text-primary-400 font-semibold";
            }

            return "text-nothing-black-500 dark:text-nothing-black-400";
        }

        /// <summary>
        /// Updates notification counts (can be called from other components)
        /// </summary>
        public async Task UpdateNotificationCounts(int? unreadMessages = null, int? friendRequests = null)
        {
            if (unreadMessages.HasValue)
            {
                unreadMessagesCount = unreadMessages.Value;
                await StorageService.SetValue(unreadMessagesCount.ToString(), "unread_messages_count");
            }

            if (friendRequests.HasValue)
            {
                friendRequestsCount = friendRequests.Value;
                await StorageService.SetValue(friendRequestsCount.ToString(), "friend_requests_count");
            }

            StateHasChanged();
        }

        /// <summary>
        /// Forces a refresh of the authentication state
        /// </summary>
        public async Task RefreshAuthenticationState()
        {
            await CheckAuthenticationState();
            await LoadNotificationCounts();
            StateHasChanged();
        }

        public void Dispose()
        {
            Navigation.LocationChanged -= OnLocationChanged;
        }
    }
}
