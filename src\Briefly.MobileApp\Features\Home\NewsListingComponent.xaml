﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="ModelFury.Briefly.MobileApp.Features.Home.NewsListingComponent"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:ModelFury.Briefly.MobileApp.Features.Home"
    Title="Ai Summarized News"
    x:DataType="local:NewsListingComponent"
    BackgroundColor="#004f98"
    Shell.BackgroundColor="#004f98"
    Shell.TitleColor="White">

    <Border BackgroundColor="{AppThemeBinding Light={StaticResource Gray50}, Dark={StaticResource Gray700}}" StrokeThickness="0">
        <Border.StrokeShape>
            <RoundRectangle CornerRadius="16,16,0,0" />
        </Border.StrokeShape>
        <Grid
            Padding="8"
            RowDefinitions="Auto, *"
            VerticalOptions="Fill">
            <Grid ColumnDefinitions="*,40">
                <Entry
                    x:Name="txtSearch"
                    IsPassword="{Binding SelectedItem.IsPassword}"
                    Placeholder="Search News"
                    Text="{Binding SelectedItem.PassKey}" />
                <ImageButton
                    Grid.Column="1"
                    BackgroundColor="{StaticResource Gray300}"
                    Clicked="ImageButton_Clicked"
                    CornerRadius="8"
                    MaximumHeightRequest="44"
                    MaximumWidthRequest="52"
                    Source="{Binding SelectedItem.SearchIcon}" />
            </Grid>
            <CollectionView
                Grid.Row="1"
                ItemsSource="{Binding NewsItems}"
                SelectionMode="Single">
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="local:NewsItemViewModel">
                        <VerticalStackLayout Padding="10">
                            <VerticalStackLayout.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type local:NewsListingComponent}}, Path=BindingContext.ReaArticleCommand}" CommandParameter="{Binding .}" />
                            </VerticalStackLayout.GestureRecognizers>
                            <Label
                                FontAttributes="Bold"
                                FontSize="Medium"
                                Text="{Binding Title}" />
                            <Label
                                FontSize="Small"
                                Text="{Binding PubDate}"
                                TextColor="{AppThemeBinding Light={StaticResource Gray700}, Dark={StaticResource Gray100}}" />
                            <Label
                                FontSize="Small"
                                LineBreakMode="TailTruncation"
                                MaxLines="3"
                                Text="{Binding Description}" TextColor="{AppThemeBinding Light={StaticResource Gray800}, Dark={StaticResource Gray50}}"/>
                        </VerticalStackLayout>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </Grid>
    </Border>
</ContentPage>
