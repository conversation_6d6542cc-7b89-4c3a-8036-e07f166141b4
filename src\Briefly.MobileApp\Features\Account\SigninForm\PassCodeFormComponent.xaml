<?xml version="1.0" encoding="utf-8" ?>
<local:PassCodeViewModelBase
    x:Class="ModelFury.Briefly.MobileApp.Features.Account.PassCodeFormComponent"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:ModelFury.Briefly.MobileApp.Features.Account"
    Title="Verify Captcha"
    BackgroundColor="#********">
    <Grid Margin="16">
        <Border
            BackgroundColor="White"
            HeightRequest="320"
            VerticalOptions="Center">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="16" />
            </Border.StrokeShape>
            <Grid
                Padding="20"
                RowDefinitions="Auto,Auto,Auto,Auto,64"
                RowSpacing="15">

                <!--  Title / Instruction  -->
                <Label
                    Grid.Row="0"
                    FontAttributes="Bold"
                    FontSize="18"
                    HorizontalOptions="Center"
                    Text="Verify Captcha" />

                <!--<Image
                    HeightRequest="16"
                    HorizontalOptions="End"
                    Source="xmark_solid.svg"
                    WidthRequest="24" />-->

                <Image Grid.Row="1" Source="captcha1.png" />


                <Border
                    Grid.Row="2"
                    Grid.Column="0"
                    Margin="4"
                    BackgroundColor="#F9F9F9">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <Entry
                        x:Name="Box0"
                        BackgroundColor="Transparent"
                        FontSize="24"
                        HorizontalTextAlignment="Center"
                        IsPassword="True"
                        Text="{Binding SelectedItem.PassKey}"
                        VerticalTextAlignment="Center" />
                </Border>



                <Button
                    Grid.Row="3"
                    BackgroundColor="{StaticResource Primary}"
                    Command="{Binding SaveCommand}"
                    CornerRadius="12"
                    HorizontalOptions="Center"
                    Text="Verify"
                    TextColor="White"
                    WidthRequest="120" />

                <Label
                    Grid.Row="4"
                    Padding="0,16,0,8"
                    FontSize="Small"
                    Text="{Binding SelectedItem.NickNameHint}"
                    TextColor="Silver" />
            </Grid>
        </Border>
    </Grid>
</local:PassCodeViewModelBase>