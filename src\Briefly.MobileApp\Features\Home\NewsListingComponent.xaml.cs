﻿using DeepMessage.Client.Common.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Maui.Platform;
using ModelFury.Briefly.MobileApp.Features.Account;
using Platform.Client.Data.EF;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Input;
using System.Xml.Linq;
namespace ModelFury.Briefly.MobileApp.Features.Home;


public partial class NewsListingComponent : ContentPage
{

    private readonly RssService _rssService = new RssService();
    public ObservableCollection<NewsItemViewModel> NewsItems { get; } = new();
    public ICommand RefreshCommand { get; }

    public ICommand ReaArticleCommand { get; }


    public IServiceScopeFactory ScopeFactory { get; }

    private async Task<IEnumerable<NewsItemViewModel>> LoadLocalNewsAsync()
    {
        var scope = ScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var news = await context.NewsItems
            .Select(x => new NewsItemViewModel
            {
                Title = x.Title,
                Description = x.Description,
                Link = x.Link,
                PubDate = x.PubDate
            })
            .ToListAsync();
        return news;
    }

    public SignInFormViewModel SelectedItem { get; set; }

    public NewsListingComponent(IServiceScopeFactory scopeFactory)
    {
        RefreshCommand = new Command<bool>(async (isBusy) =>
        {
            await MainThread.InvokeOnMainThreadAsync(() =>
            {
                IsBusy = true;
            });

            var t = await Task.Factory.StartNew(async () =>
            {
                var newsItems = await _rssService.GetNewsAsync(); ;
                foreach (var item in newsItems)
                {
                    try
                    {
                        var scope = ScopeFactory.CreateScope();
                        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                        var localNewsItem = await context.NewsItems.FirstOrDefaultAsync(x => x.Link == item.Link);
                        if (localNewsItem == null)
                        {
                            context.NewsItems.Add(new NewsItem()
                            {
                                Id = Guid.NewGuid().ToString(),
                                Title = item.Title ?? string.Empty,
                                Description = item.Description ?? string.Empty,
                                Link = item.Link ?? string.Empty,
                                PubDate = item.PubDate
                            });
                            await context.SaveChangesAsync();
                        }
                    }
                    catch
                    {
                        // ignore which cannot be stored
                    }
                }
            });

            await t.ContinueWith(async x =>
            {
                var localNews = await LoadLocalNewsAsync();
                await MainThread.InvokeOnMainThreadAsync(() =>
                {
                    foreach (var item in localNews)
                    {
                        NewsItems.Add(item);
                    }
                    IsBusy = false;
                });
            });

        });

        ReaArticleCommand = new Command(async (p) =>
        {
            if (p is NewsItemViewModel)
            {
                var newsItem = p as NewsItemViewModel;
                await Launcher.Default.OpenAsync(newsItem.Link!);
            }
        });
        var t1 = Task.Factory.StartNew(async () =>
        {
            var localNews = await LoadLocalNewsAsync();
            await MainThread.InvokeOnMainThreadAsync(() =>
            {
                foreach (var item in localNews)
                {
                    NewsItems.Add(item);
                }
            });

        });

        t1.ContinueWith(x =>
        {
            RefreshCommand.Execute(false);

        });
        ScopeFactory = scopeFactory;

        InitializeComponent();
        SetStatusBarColor(Color.FromArgb("004f98"));
        SelectedItem = new SignInFormViewModel();
        SelectedItem.PropertyChanged += SelectedItem_PropertyChanged;
        BindingContext = this;
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();

        // Small delay helps ensure the page is fully loaded
        await Task.Delay(200);

        txtSearch.Focus(); // Triggers keyboard to show
    }

    void SetStatusBarColor(Color color)
    {
#if ANDROID
        var window = Microsoft.Maui.ApplicationModel.Platform.CurrentActivity?.Window;

        if (window != null)
        {
            window.ClearFlags(Android.Views.WindowManagerFlags.TranslucentStatus);
            window.AddFlags(Android.Views.WindowManagerFlags.DrawsSystemBarBackgrounds);
            window.SetStatusBarColor(color.ToPlatform()); // Ensure the namespace is included  
        }
#endif
    }


    public void SelectedItem_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (SelectedItem.IsPassword && e.PropertyName == nameof(SelectedItem.IsPassword))
        {
            var popup = new PassCodeFormComponent(ScopeFactory);
            _ = Navigation.PushModalAsync(popup, false);
        }
    }
    private void ImageButton_Clicked(object sender, EventArgs e)
    {
        Vibration.Vibrate(TimeSpan.FromMilliseconds(80));
        if (txtSearch.Text == null)
            return;

    }

}

public class RssService
{
    private const string FeedUrl = "https://feeds.bbci.co.uk/news/rss.xml";

    public async Task<List<NewsItemViewModel>> GetNewsAsync()
    {
        try
        {
            var client = new HttpClient();
            var response = await client.GetStringAsync(FeedUrl);
            var doc = XDocument.Parse(response);

            var items = doc.Descendants("item").Select(item => new NewsItemViewModel
            {
                Title = item.Element("title")?.Value,
                Description = item.Element("description")?.Value,
                Link = item.Element("link")?.Value,
                PubDate = DateTime.TryParse(item.Element("pubDate")?.Value, out var date) ? date : DateTime.MinValue
            }).ToList();

            return items;
        }
        catch
        {
            return new List<NewsItemViewModel>();
        }
    }
}

public class NewsItemViewModel
{
    public string? Title { get; set; } = null!;
    public string? Description { get; set; } = null!;
    public string? Link { get; set; } = null!;
    public DateTime PubDate { get; set; }
}
