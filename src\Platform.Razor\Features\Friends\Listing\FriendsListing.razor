@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.Friends 
@using Platform.Client.Services.Features.Friends
@page "/friends"
@inherits ListingBase<FriendsListingViewModel, FriendsListingBusinessObject, FriendsFilterViewModel, FriendsFilterBusinessObject, IFriendsListingDataService>
 
<div class="min-h-screen bg-nothing-black-900 animate-fade-in2">
    
    <div >
        <div class="px-4 py-4">
            <!-- Title with Add Friend Button -->
            <div class="flex items-center justify-between mb-4">
                <h1 class="text-2xl font-semibold text-white">Friends</h1>
                <button @onclick="ShowAddFriendForm"
                        class="p-2 rounded-full bg-white/20 hover:bg-white/30">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </button>
            </div>
             
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-4 w-4 text-nothing-black-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <input type="text"
                       @bind="FilterViewModel.SearchText"
                       @bind:event="oninput"
                       @onkeyup="OnSearchKeyUp"
                       placeholder="Search friends..."
                       class="w-full pl-10 pr-10 py-2.5 bg-white dark:bg-nothing-black-700 border-0 rounded-lg text-nothing-black-900 dark:text-white placeholder-nothing-black-400 focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-nothing-black-500 text-sm" />
                @if (!string.IsNullOrEmpty(FilterViewModel.SearchText))
                {
                    <button @onclick="ClearSearch"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <svg class="h-4 w-4 text-nothing-black-400 hover:text-nothing-black-600 " fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                }
            </div>
        </div>
    </div>
     
    <div class="bg-white dark:bg-nothing-black-900 min-h-screen">
        @if (IsWorking)
        {
            <!-- Loading State - Minimalistic -->
            <div class="flex items-center justify-center py-16">
                <div class="flex items-center space-x-3">
                    <div class="animate-spin rounded-full h-6 w-6 border-2 border-primary-500 border-t-transparent"></div>
                    <span class="text-nothing-black-600 dark:text-nothing-black-300">Loading friends...</span>
                </div>
            </div>
        }
        else if (!string.IsNullOrEmpty(Error))
        {
            <!-- Error State - Minimalistic -->
            <div class="p-4">
                <div class="bg-nothing-red-50 dark:bg-nothing-red-900/20 border border-nothing-red-200 dark:border-nothing-red-800 rounded-lg p-4">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 text-nothing-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-nothing-red-800 dark:text-nothing-red-200">Error loading friends</h3>
                            <p class="mt-1 text-sm text-nothing-red-700 dark:text-nothing-red-300">@Error</p>
                        </div>
                    </div>
                    <button @onclick="RefreshItems"
                            class="mt-3 bg-nothing-red-500 hover:bg-nothing-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                        Try Again
                    </button>
                </div>
            </div>
        }
        else if (Items.Count == 0)
        {
            <!-- Empty State - WhatsApp Style -->
            <div class="text-center py-16 px-4">
                <svg class="mx-auto h-16 w-16 text-nothing-black-300 dark:text-nothing-black-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <h3 class="mt-6 text-lg font-medium text-nothing-black-900 dark:text-white">No friends found</h3>
                <p class="mt-2 text-sm text-nothing-black-500 dark:text-nothing-black-400 max-w-sm mx-auto">
                    @if (!string.IsNullOrEmpty(FilterViewModel.SearchText))
                    {
                        <span>No friends match your search for "@FilterViewModel.SearchText"</span>
                    }
                    else
                    {
                        <span>You haven't added any friends yet. Start by adding your first friend!</span>
                    }
                </p>
                @if (!string.IsNullOrEmpty(FilterViewModel.SearchText))
                {
                    <button @onclick="ClearSearch"
                            class="mt-6 bg-nothing-black-700 hover:bg-nothing-black-800 text-white px-6 py-2.5 rounded-lg text-sm font-medium transition-colors duration-200">
                        Clear Search
                    </button>
                }
                else
                {
                    <button @onclick="ShowAddFriendForm"
                            class="mt-6 bg-primary-500 hover:bg-primary-600 text-white px-6 py-2.5 rounded-lg text-sm font-medium transition-colors duration-200">
                        Add Your First Friend
                    </button>
                }
            </div>
        }
        else
        {
            <!-- Search Results Info - Minimalistic -->
            @if (!string.IsNullOrEmpty(FilterViewModel.SearchText))
            {
                <div class="px-4 py-3 bg-nothing-black-100 dark:bg-nothing-black-800 border-b border-nothing-black-200 dark:border-nothing-black-700">
                    <p class="text-sm text-nothing-black-600 dark:text-nothing-black-300">
                        <span class="font-medium">@TotalRecords</span> friends found for
                        <span class="font-medium">"@FilterViewModel.SearchText"</span>
                        <button @onclick="ClearSearch" class="ml-2 text-primary-600 dark:text-primary-400 hover:underline">
                            Clear search
                        </button>
                    </p>
                </div>
            }

            <!-- Friends List - WhatsApp Row-Based Layout -->
            <div class="divide-y divide-nothing-black-200 dark:divide-nothing-black-700">
                @foreach (var friend in Items)
                {
                    <div class="px-4 py-3 hover:bg-nothing-black-100 dark:hover:bg-nothing-black-800 transition-colors duration-150 cursor-pointer animate-fade-in">
                        <div class="flex items-center space-x-3">
                            <!-- Profile Picture -->
                            <div class="relative flex-shrink-0">
                                @if (!string.IsNullOrEmpty(friend.Avatar))
                                {
                                    <img src="@friend.Avatar" alt="@friend.Name"
                                         class="w-12 h-12 rounded-full object-cover" />
                                }
                                else
                                {
                                    <div class="w-12 h-12 rounded-full bg-nothing-black-300 dark:bg-nothing-black-600 flex items-center justify-center text-white font-medium text-lg">
                                        @GetInitials(friend.Name)
                                    </div>
                                }
                                <!-- Online Status Indicator -->
                                <div class="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-primary-500 border-2 border-white dark:border-nothing-black-900 rounded-full"></div>
                            </div>

                            <!-- Friend Info -->
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-base font-medium text-nothing-black-900 dark:text-white truncate">@friend.Name</h3>
                                </div>
                                @if (!string.IsNullOrEmpty(friend.Status))
                                {
                                    <p class="text-sm text-nothing-black-500 dark:text-nothing-black-400 truncate mt-0.5">@friend.Status</p>
                                }
                            </div>

                            <!-- Chat Action Button -->
                            <div class="flex-shrink-0">
                                <button @onclick="() => StartChat(friend)"
                                        class="p-2 rounded-full bg-primary-500 hover:bg-primary-600 text-white transition-colors duration-200 shadow-sm">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
    </div>
</div>
