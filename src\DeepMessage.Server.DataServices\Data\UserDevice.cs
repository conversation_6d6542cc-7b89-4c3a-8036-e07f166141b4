﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DeepMessage.Server.DataServices.Data
{
    public class UserDevice
    {
        [StringLength(450), Key]
        public string Id { get; set; } = string.Empty;

        [StringLength(450)]
        public string UserId { get; set; } = string.Empty;

        [StringLength(450)]
        public string Name { get; set; } = string.Empty;

        [StringLength(450)]
        public string? DeviceToken { get; set; }

        [StringLength(450)]
        public string? Platform { get; set; }

        public DateTime LastLogin { get; set; }

        public DateTime LastSync { get; set; }

        public int Status { get; set; }

        public DateTime? StatusAppliedAt { get; set; }

        public string? StatusLog { get; set; }
    }
}
