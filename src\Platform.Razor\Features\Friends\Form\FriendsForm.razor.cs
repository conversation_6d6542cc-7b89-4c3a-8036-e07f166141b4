using Microsoft.AspNetCore.Components;

namespace Platform.Razor.Features.Friends.Form
{
    public partial class FriendsForm
    { 
        [Inject] private NavigationManager Navigation { get; set; } = null!;
  
        public override async Task OnAfterSaveAsync(string key)
        {
            // Navigate back to friends list after successful save
            await base.OnAfterSaveAsync(key);
            
            // Show success message and navigate
            Navigation.NavigateTo("/friends");
        }

        /// <summary>
        /// Navigates back to the friends list
        /// </summary>
        private void GoBack()
        {
            Navigation.NavigateTo("/friends");
        }
         
    }
}
