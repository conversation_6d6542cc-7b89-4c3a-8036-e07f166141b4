using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Platform.Framework.Core;
using Platform.Razor.Features.Navigation.BottomTabs;

namespace Platform.Razor.Pages
{
    public partial class TabNavigationDemo
    {


        private bool isAuthenticated = false;
        private BottomTabNavigation? bottomTabNavigation; 
        [Inject] private IJSRuntime JSRuntime { get; set; } = null!; 
        protected override async Task OnInitializedAsync()
        {
            await CheckAuthenticationStatus();
        }

        /// <summary>
        /// Checks the current authentication status
        /// </summary>
        private async Task CheckAuthenticationStatus()
        {
            try
            {
                var authToken = await StorageService.GetValue("auth_token");
                isAuthenticated = !string.IsNullOrEmpty(authToken);
            }
            catch
            {
                isAuthenticated = false;
            }
        }

        /// <summary>
        /// Simulates user login
        /// </summary>
        private async Task SimulateLogin()
        {
            try
            {
                // Set mock authentication data
                await StorageService.SetValue("mock_auth_token_12345", "auth_token");
                await StorageService.SetValue("mock_refresh_token", "refresh_token");
                await StorageService.SetValue("demo_user_123", "user_id");
                await StorageService.SetValue("demouser", "username");

                // Set some notification counts
                await StorageService.SetValue("3", "unread_messages_count");
                await StorageService.SetValue("1", "friend_requests_count");

                isAuthenticated = true;

                // Refresh the bottom tab navigation
                if (bottomTabNavigation != null)
                {
                    await bottomTabNavigation.RefreshAuthenticationState();
                }

                StateHasChanged();
            }
            catch (Exception)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// Simulates user logout
        /// </summary>
        private async Task SimulateLogout()
        {
            try
            {
                // Clear authentication data
                await StorageService.RemoveValue("auth_token");
                await StorageService.RemoveValue("refresh_token");
                await StorageService.RemoveValue("user_id");
                await StorageService.RemoveValue("username");

                // Clear notification counts
                await StorageService.RemoveValue("unread_messages_count");
                await StorageService.RemoveValue("friend_requests_count");

                isAuthenticated = false;

                // Refresh the bottom tab navigation
                if (bottomTabNavigation != null)
                {
                    await bottomTabNavigation.RefreshAuthenticationState();
                }

                StateHasChanged();
            }
            catch (Exception)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// Updates notification badge counts
        /// </summary>
        private async Task UpdateNotificationCounts()
        {
            try
            {
                if (!isAuthenticated) return;

                // Generate random notification counts
                var random = new Random();
                var messageCount = random.Next(0, 10);
                var friendRequestCount = random.Next(0, 5);

                // Update the bottom tab navigation
                if (bottomTabNavigation != null)
                {
                    await bottomTabNavigation.UpdateNotificationCounts(messageCount, friendRequestCount);
                }
            }
            catch (Exception)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// Toggles dark mode
        /// </summary>
        private async Task ToggleDarkMode()
        {
            try
            {
                await JSRuntime.InvokeVoidAsync("eval", @"
                    if (document.documentElement.classList.contains('dark')) {
                        document.documentElement.classList.remove('dark');
                        localStorage.setItem('dark_mode_enabled', 'false');
                    } else {
                        document.documentElement.classList.add('dark');
                        localStorage.setItem('dark_mode_enabled', 'true');
                    }
                ");
            }
            catch
            {
                // Ignore errors
            }
        }
    }
}
