using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace Platform.Client.Services.Services
{
    public interface IClientEncryptionService
    {
        RSAKeyPair GenerateRSAKeyPairAsync();
        byte[] DeriveAESKeyFromPasswordAsync(string password, byte[]? salt = null);
        string EncryptWithAESAsync(string plaintext, byte[] key);
        string DecryptWithAESAsync(string ciphertext, byte[] key);
        string EncryptRSAPrivateKeyAsync(string privateKeyPem, byte[] aesKey);
        string DecryptRSAPrivateKeyAsync(string encryptedPrivateKey, byte[] aesKey);
        byte[] GetSaltFromEncryptedDataAsync(string encryptedData);
    }

    public class RSAKeyPair
    {
        public string PublicKeyPem { get; set; } = string.Empty;
        public string PrivateKeyPem { get; set; } = string.Empty;
    }

    public class ClientEncryptionService : IClientEncryptionService
    {
        private const int PBKDF2_ITERATIONS = 100000;
        private const int SALT_SIZE = 32;
        private const int AES_KEY_SIZE = 32; // 256 bits
        private const int IV_SIZE = 16; // 128 bits

        public RSAKeyPair GenerateRSAKeyPairAsync()
        {
            using var rsa = RSA.Create(2048);

            var publicKeyPem = Convert.ToBase64String(rsa.ExportRSAPublicKey());
            var privateKeyPem = Convert.ToBase64String(rsa.ExportRSAPrivateKey());

            return new RSAKeyPair
            {
                PublicKeyPem = publicKeyPem,
                PrivateKeyPem = privateKeyPem
            };
        }

        public byte[] DeriveAESKeyFromPasswordAsync(string password, byte[]? salt = null)
        {
            if (salt == null)
            {
                salt = new byte[SALT_SIZE];
                using var rng = RandomNumberGenerator.Create();
                rng.GetBytes(salt);
            }

            using var pbkdf2 = new Rfc2898DeriveBytes(
                Encoding.UTF8.GetBytes(password),
                salt,
                PBKDF2_ITERATIONS,
                HashAlgorithmName.SHA256);

            return pbkdf2.GetBytes(AES_KEY_SIZE);
        }

        public string EncryptWithAESAsync(string plaintext, byte[] key)
        {
            using var aes = Aes.Create();
            aes.Key = key;
            aes.GenerateIV();

            using var encryptor = aes.CreateEncryptor();
            var plaintextBytes = Encoding.UTF8.GetBytes(plaintext);
            var ciphertextBytes = encryptor.TransformFinalBlock(plaintextBytes, 0, plaintextBytes.Length);

            // Combine IV + ciphertext
            var result = new byte[IV_SIZE + ciphertextBytes.Length];
            Array.Copy(aes.IV, 0, result, 0, IV_SIZE);
            Array.Copy(ciphertextBytes, 0, result, IV_SIZE, ciphertextBytes.Length);

            return Convert.ToBase64String(result);
        }

        public string DecryptWithAESAsync(string ciphertext, byte[] key)
        {
            var data = Convert.FromBase64String(ciphertext);

            // Extract IV and ciphertext
            var iv = new byte[IV_SIZE];
            var ciphertextBytes = new byte[data.Length - IV_SIZE];
            Array.Copy(data, 0, iv, 0, IV_SIZE);
            Array.Copy(data, IV_SIZE, ciphertextBytes, 0, ciphertextBytes.Length);

            using var aes = Aes.Create();
            aes.Key = key;
            aes.IV = iv;

            using var decryptor = aes.CreateDecryptor();
            var plaintextBytes = decryptor.TransformFinalBlock(ciphertextBytes, 0, ciphertextBytes.Length);

            return Encoding.UTF8.GetString(plaintextBytes);
        }

        public string EncryptRSAPrivateKeyAsync(string privateKeyPem, byte[] aesKey)
        {
            return EncryptWithAESAsync(privateKeyPem, aesKey);
        }

        public string DecryptRSAPrivateKeyAsync(string encryptedPrivateKey, byte[] aesKey)
        {
            return DecryptWithAESAsync(encryptedPrivateKey, aesKey);
        }

        public byte[] GetSaltFromEncryptedDataAsync(string encryptedData)
        {
            // For PBKDF2, we'll store salt separately in local storage
            // This method is for future extensibility
            // Return empty array for now since salt is stored separately
            return Array.Empty<byte>();
        }
    }

    public class EncryptedCredentials
    {
        public string EncryptedUsername { get; set; } = string.Empty;
        public byte[] Salt { get; set; } = Array.Empty<byte>();
        public string UserId { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
    }
}
