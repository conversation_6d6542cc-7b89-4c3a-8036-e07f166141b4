using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using System.Text.Json;
using DeepMessage.ServiceContracts.Features.Account;
using ModelFury.Briefly.MobileApp.Features.Account;
using Microsoft.JSInterop;
using DeepMessage.Client.Common.Data;
using System.Security.Claims;
using Microsoft.Extensions.DependencyInjection;
using Platform.Framework.Core;

namespace Platform.Razor.Features.Authentication.SignIn
{
    public partial class SignInForm
    {

        //protected override async Task OnInitializedAsync()
        //{
        //    // Check if user is already authenticated
        //    var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        //    if (authState.User.Identity?.IsAuthenticated == true)
        //    {
        //        Navigation.NavigateTo("/", replace: true);
        //        return;
        //    }

        //    await base.OnInitializedAsync();
        //}

        protected override async Task<SignInFormViewModel> CreateSelectedItem()
        {
            return await Task.FromResult(new SignInFormViewModel
            { 
                PassKey = string.Empty, 
                ShowPassword = false,
                DeviceString = Guid.NewGuid().ToString(),
            });
        }
         

        /// <summary>
        /// Toggles password visibility
        /// </summary>
        private void TogglePasswordVisibility()
        {
            if (SelectedItem != null)
            {
                SelectedItem.ShowPassword = !SelectedItem.ShowPassword;
            }
        }
 

        /// <summary>
        /// Handles successful authentication
        /// </summary>
        public override async Task OnAfterSaveAsync(string authResult)
        {
            try
            {
                if (authResult == "Authenticate Online")
                {
                    var scope = ScopeFactory.CreateScope();
                    var onlineService = scope.ServiceProvider.GetKeyedService<ISignInFormDataService>("backup");
                    if (onlineService == null)
                        throw new InvalidOperationException("Backup authentication service not found");

                    authResult = await onlineService.SaveAsync(ConvertViewModelToBusinessModel(SelectedItem));
                }

                // Parse the authentication result
                var authClaims = JsonSerializer.Deserialize<AuthorizationClaimsModel>(authResult);
                
                if (authClaims != null)
                {
                    // Store authentication tokens (implementation depends on your auth system)
                    await StoreAuthenticationTokens(authClaims);
                    
                    // Notify authentication state provider of the change
                    if (AuthStateProvider is IAuthenticationStateNotifier notifier)
                    {
                        await notifier.NotifyAuthenticationStateChanged();
                    }
                    
                    // Navigate to the intended page or dashboard
                    var returnUrl = GetReturnUrl();
                    Navigation.NavigateTo(returnUrl, replace: true);
                }
            }
            catch (Exception ex)
            {
                //Logger.LogError(ex, "Error processing authentication result");
                Error = "Authentication succeeded but there was an error processing the result. Please try again.";
            }
        }

        /// <summary>
        /// Stores authentication tokens
        /// </summary>
        private async Task StoreAuthenticationTokens(AuthorizationClaimsModel authClaims)
        {
            try
            {
                var scope = ScopeFactory.CreateScope();
                var localStorage = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
                await localStorage.SetValue(authClaims.Token, "auth_token");
                await localStorage.SetValue(authClaims.UserId, ClaimTypes.NameIdentifier);
                await localStorage.SetValue(authClaims.Username, ClaimTypes.Name);

                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                var user = await context.ApplicationUsers.FindAsync(authClaims.UserId);
                if (user == null)
                {
                    user = new ApplicationUser()
                    {
                        Id = authClaims.UserId,
                        NickName = authClaims.Username,
                        Hash = "dummy",
                    };
                    context.ApplicationUsers.Add(user);
                    await context.SaveChangesAsync();
                }

            }
            catch (Exception ex)
            {
                //Logger.LogError(ex, "Error storing authentication tokens");
                // Don't throw here as authentication was successful
            }
        }

        /// <summary>
        /// Gets the return URL from query parameters
        /// </summary>
        private string GetReturnUrl()
        {
            var uri = new Uri(Navigation.Uri);
            var query = System.Web.HttpUtility.ParseQueryString(uri.Query);
            var returnUrl = query["returnUrl"];
            
            // Validate return URL to prevent open redirect attacks
            if (!string.IsNullOrEmpty(returnUrl) && Uri.IsWellFormedUriString(returnUrl, UriKind.Relative))
            {
                return returnUrl;
            }
            
            return "/"; // Default to home page
        }

        /// <summary>
        /// Custom error handling for authentication failures
        /// </summary>
        //protected override void LogAndDisplayError(Exception ex)
        //{
        //    Logger.LogError(ex, "Sign-in error");
            
        //    Error = ex switch
        //    {
        //        UnauthorizedAccessException => "Invalid email/username or password. Please check your credentials and try again.",
        //        ArgumentException => "Please check your input and try again.",
        //        InvalidOperationException => "Unable to sign in at this time. Please try again later.",
        //        TimeoutException => "The sign-in request timed out. Please check your connection and try again.",
        //        _ => "Sign-in failed. Please check your credentials and try again."
        //    };
        //}

        /// <summary>
        /// Validates form before submission
        /// </summary>
        //protected override async Task<bool> ValidateForm()
        //{
        //    if (SelectedItem == null)
        //        return false;

        //    // Set NickName from Email for backward compatibility
        //    if (string.IsNullOrEmpty(SelectedItem.NickName) && !string.IsNullOrEmpty(SelectedItem.Email))
        //    {
        //        SelectedItem.NickName = SelectedItem.Email;
        //    }

        //    return await base.ValidateForm();
        //}

       
    }

    /// <summary>
    /// Interface for authentication state notification
    /// </summary>
    public interface IAuthenticationStateNotifier
    {
        Task NotifyAuthenticationStateChanged();
    }
}
