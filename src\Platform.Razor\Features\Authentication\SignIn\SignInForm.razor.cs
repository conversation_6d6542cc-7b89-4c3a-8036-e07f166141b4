using DeepMessage.Client.Common.Data;
using DeepMessage.ServiceContracts.Features.Account;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.JSInterop;
using ModelFury.Briefly.MobileApp.Features.Account;
using Platform.Client.Services.Services;
using Platform.Framework.Core;
using System.Security.Claims;
using System.Text.Json;

namespace Platform.Razor.Features.Authentication.SignIn
{
    public partial class SignInForm
    { 
        [Inject]
        IClientEncryptionService _encryptionService { get; set; } = default!;

        [Inject]
        ISecureKeyManager _secureKeyManager { get; set; } = default!;

        public override string? ServiceKey => "client";

        //protected override async Task OnInitializedAsync()
        //{
        //    // Check if user is already authenticated
        //    var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        //    if (authState.User.Identity?.IsAuthenticated == true)
        //    {
        //        Navigation.NavigateTo("/", replace: true);
        //        return;
        //    }

        //    await base.OnInitializedAsync();
        //}

        protected override async Task<SignInFormViewModel> CreateSelectedItem()
        {
            return await Task.FromResult(new SignInFormViewModel
            {
                PassKey = string.Empty,
                ShowPassword = false,
                DeviceString = Guid.NewGuid().ToString(),
            });
        }

        protected override SigninFormBusinessObject ConvertViewModelToBusinessModel(SignInFormViewModel formViewModel)
        {
            var passKey = formViewModel.PassKey?.Trim();
            if (!string.IsNullOrEmpty(formViewModel.NickName))
            {
                passKey = $"{formViewModel.NickName}{_encryptionService.GenerateBitSignature(SelectedItem.PassKey!)}!";
            }
            ;
            return new SigninFormBusinessObject()
            {
                NickName = formViewModel.NickName.Trim(),
                DeviceString = formViewModel.DeviceString,
                PassKey = passKey
            };
        }

        /// <summary>
        /// Toggles password visibility
        /// </summary>
        private void TogglePasswordVisibility()
        {
            if (SelectedItem != null)
            {
                SelectedItem.ShowPassword = !SelectedItem.ShowPassword;
            }
        }


        /// <summary>
        /// Handles successful authentication
        /// </summary>
        public override async Task OnAfterSaveAsync(string authResult)
        {
            var authClaims = JsonSerializer.Deserialize<AuthorizationClaimsModel>(authResult);

            if (authClaims != null)
            {
                // Store authentication tokens (implementation depends on your auth system)
                await StoreAuthenticationTokens(authClaims);

                //Initialize secure key manager with the keys in memory
                await _secureKeyManager.DeriveAndStoreKeysAsync(SelectedItem.NickName!, SelectedItem.PassKey!);
                // Notify authentication state provider of the change
                if (AuthStateProvider is IAuthenticationStateNotifier notifier)
                {
                    await notifier.NotifyAuthenticationStateChanged();
                }

                Navigation.NavigateTo("/chat", replace: true);
            }
        }

        /// <summary>
        /// Stores authentication tokens
        /// </summary>
        private async Task StoreAuthenticationTokens(AuthorizationClaimsModel authClaims)
        {
            try
            {
                var scope = ScopeFactory.CreateScope();
                var localStorage = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
                await localStorage.SetValue(authClaims.Token, "auth_token");
                await localStorage.SetValue(authClaims.UserId, ClaimTypes.NameIdentifier);
                await localStorage.SetValue(authClaims.Username, ClaimTypes.Name);
                await localStorage.SetValue(authClaims.Pub1, "pub1o_");
                await localStorage.SetValue(authClaims.Pub2, "pub2e_");

                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                var user = await context.ApplicationUsers.FindAsync(authClaims.UserId);
                if (user == null)
                {
                    user = new ApplicationUser()
                    {
                        Id = authClaims.UserId,
                        NickName = authClaims.Username,
                        Hash = "dummy",
                    };
                    context.ApplicationUsers.Add(user);
                    await context.SaveChangesAsync();
                }

            }
            catch (Exception ex)
            {
                //Logger.LogError(ex, "Error storing authentication tokens");
                // Don't throw here as authentication was successful
            }
        }

        

        /// <summary>
        /// Custom error handling for authentication failures
        /// </summary>
        //protected override void LogAndDisplayError(Exception ex)
        //{
        //    Logger.LogError(ex, "Sign-in error");

        //    Error = ex switch
        //    {
        //        UnauthorizedAccessException => "Invalid email/username or password. Please check your credentials and try again.",
        //        ArgumentException => "Please check your input and try again.",
        //        InvalidOperationException => "Unable to sign in at this time. Please try again later.",
        //        TimeoutException => "The sign-in request timed out. Please check your connection and try again.",
        //        _ => "Sign-in failed. Please check your credentials and try again."
        //    };
        //}

        /// <summary>
        /// Validates form before submission
        /// </summary>
        //protected override async Task<bool> ValidateForm()
        //{
        //    if (SelectedItem == null)
        //        return false;

        //    // Set NickName from Email for backward compatibility
        //    if (string.IsNullOrEmpty(SelectedItem.NickName) && !string.IsNullOrEmpty(SelectedItem.Email))
        //    {
        //        SelectedItem.NickName = SelectedItem.Email;
        //    }

        //    return await base.ValidateForm();
        //}


    }

    /// <summary>
    /// Interface for authentication state notification
    /// </summary>
    public interface IAuthenticationStateNotifier
    {
        Task NotifyAuthenticationStateChanged();
    }
}
