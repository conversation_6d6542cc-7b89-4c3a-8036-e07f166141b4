﻿using DeepMessage.Client.Common.Data;
using Platform.Client.Common.Features.Friends;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Friends;
using System.Security.Claims;
using System.Text.Json;
using Platform.Client.Services.Features.Friends;
using Platform.Framework.Core;
namespace Platform.Client.Common.Features.Friends;
public class FriendFormViewBase : FormBaseMaui<FriendFormBusinessObject, FriendFormViewModel, string, IFriendFormDataService>
{
    public FriendFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }

    public override async Task OnAfterSaveAsync(string key)
    {
        try
        {
            //lets try pulling from the server
            using var scope = ScopeFactory.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            var localStorage = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();

            // Deserialize the new FriendFormResponseDto format
            var friendResponse = JsonSerializer.Deserialize<FriendFormResponseDto>(key) ?? throw new InvalidDataException("Friend data is null");

            // Convert to Friendship entity for local storage
            var friendship = new Friendship
            {
                Id = friendResponse.Id,
                UserId = await localStorage.GetValue(ClaimTypes.NameIdentifier) ?? string.Empty,
                FriendId = friendResponse.FriendId,
                Name = friendResponse.Name,
                DisplayPictureUrl = friendResponse.DisplayPictureUrl,
                Pub1 = friendResponse.Pub1, // Store friend's public key for encryption
                CreatedAt = friendResponse.CreatedAt
            };

            if (!context.Friendships.Any(x => x.Id == friendship.Id))
            {
                context.Friendships.Add(friendship);
                await context.SaveChangesAsync();
            }
            await MainThread.InvokeOnMainThreadAsync(() =>
              {
                  _ = Navigation.PopModalAsync();
              });
        }
        catch (Exception)
        {

        }

    }
}

public partial class FriendFormView : FriendFormViewBase
{
    public FriendFormView(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
        InitializeComponent();
        BindingContext = this;
    }
}
