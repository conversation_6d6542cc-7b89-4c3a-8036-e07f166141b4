﻿using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Enums;
using System.ComponentModel.DataAnnotations;
namespace DeepMessage.ServiceContracts.Features.Conversation;
public class ChatMessagesSyncFormBusinessObject
{
    [StringLength(450)]
    public string Id { get; set; } = null!;

    [StringLength(450)]
    public string ConversationId { get; set; } = null!;

    public string SenderId { get; set; } = null!;

    /// <summary>
    /// (Optional) Plaintext content if you store it server-side. 
    /// If you truly want E2E encryption, you might store only ephemeral or no plaintext.
    /// For demonstration, we keep it. 
    /// </summary>
    public string? PlainContent { get; set; }


    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    //public byte SyncStatus { get; set; }

    // Soft-deletion
    public bool IsDeleted { get; set; } = false;
    public DateTime? DeletedAt { get; set; }

    // If you want to track edits:
    public bool IsEdited { get; set; } = false;
    public DateTime? EditedAt { get; set; }

    // Disappearing (ephemeral) message fields
    public bool IsEphemeral { get; set; } = false;
    public TimeSpan? DisappearAfter { get; set; }
    public DateTime? DisappearAt { get; set; }

    public bool EnableFallBackChannel { get; set; }

    public string? MessageRecepientId { get; set; } // extra field for queing logic
    public string? MessageRecepientUserName { get; set; } // extra field for queing logic
    //public IEnumerable<MessageRecipientSyncFormBusinessObject> MessageRecipients { get; set; }

    //public ChatMessagesSyncFormBusinessObject()
    //{
    //    MessageRecipients = new List<MessageRecipientSyncFormBusinessObject>();
    //}
}

//public class MessageRecipientSyncFormBusinessObject
//{
//    [Key, StringLength(450)]
//    public string Id { get; set; } = null!;

//    [StringLength(450)]
//    public string MessageId { get; set; } = null!;

//    public string RecipientId { get; set; } = null!;

//    public string EncryptedContent { get; set; } = null!;

//}

public class ChatMessageUpdate
{
    public string Id { get; set; } = null!;
 
    public DeliveryStatus DeliveryStatus { get; set; }

    public DateTime DeliveryStatusTime { get; set; }

    public string SenderId { get; set; } = null!;
}