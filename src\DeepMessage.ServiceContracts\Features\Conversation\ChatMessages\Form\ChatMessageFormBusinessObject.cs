﻿using DeepMessage.Framework.Core;
using System.ComponentModel.DataAnnotations;
namespace DeepMessage.ServiceContracts.Features.Conversation;
public class ChatMessageFormBusinessObject
{
    public string? Id { get; set; }

    [Required]
    public string? ConversationId { get; set; }

    [Required]
    public string? Content { get; set; }

    public byte ContentType { get; set; }
}

public class ChatMessageIdBusinessObject
{
    public ChatMessageIdBusinessObject(string id, string conversationId, string senderId, string receiverId)
    {
        Id = id;
        ConversationId = conversationId;
        SenderId = senderId;
        ReceiverId = receiverId;
    }

    public string Id { get; set; }

    public string ConversationId { get; set; }

    public string SenderId { get; set; }

    public string ReceiverId { get; set; }



}
