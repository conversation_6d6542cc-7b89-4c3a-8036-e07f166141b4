@using Microsoft.AspNetCore.Components.Authorization 
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthStateProvider 
 
<!-- Bottom Tab Navigation - WhatsApp Minimalistic Style -->
@if (showTabs && isAuthenticated)
{
    <div class="fixed bottom-0 left-0 right-0 z-50 bg-white   border-t border-nothing-black-200  safe-area-bottom shadow-sm">
        <!-- Tab Container -->
        <div class="flex items-center justify-around px-4 py-2">
            <!-- Messages Tab -->
            <button @onclick='() => NavigateToTab("/chat")'
                    class="@GetTabClasses("/chat") flex-1 flex flex-col items-center justify-center py-2 px-2 min-h-[52px] transition-all duration-200 rounded-lg"
                    aria-label="Messages">
                <!-- Messages Icon -->
                <div class="@GetIconClasses("/chat") mb-1 relative">
                    @if (IsActiveTab("/chat"))
                    {
                        <!-- Active Messages Icon (Filled) -->
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
                        </svg>
                    }
                    else
                    {
                        <!-- Inactive Messages Icon (Outline) -->
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                        </svg>
                    }
                    @if (unreadMessagesCount > 0)
                    {
                        <div class="absolute -top-1 -right-1 bg-primary-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1 shadow-sm">
                            @(unreadMessagesCount > 99 ? "99+" : unreadMessagesCount.ToString())
                        </div>
                    }
                </div>
                <span class="@GetLabelClasses("/chat") text-xs font-medium leading-none">Messages</span>
            </button>

            <!-- Friends Tab -->
            <button @onclick='() => NavigateToTab("/friends")'
                    class="@GetTabClasses("/friends") flex-1 flex flex-col items-center justify-center py-2 px-2 min-h-[52px] transition-all duration-200 rounded-lg"
                    aria-label="Friends">
                <!-- Friends Icon -->
                <div class="@GetIconClasses("/friends") mb-1 relative">
                    @if (IsActiveTab("/friends"))
                    {
                        <!-- Active Friends Icon (Filled) -->
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM4 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm5 7c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm6.39 4.56C13.71 14.7 11.53 14 9 14s-4.71.7-6.39 1.56A2.97 2.97 0 0 0 2 18.22V21h14v-2.78c0-1.12-.61-2.15-1.61-2.66zM22 9v2h-3v3h-2v-3h-3V9h3V6h2v3h3z"/>
                        </svg>
                    }
                    else
                    {
                        <!-- Inactive Friends Icon (Outline) -->
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                        </svg>
                    }
                    @if (friendRequestsCount > 0)
                    {
                        <div class="absolute -top-1 -right-1 bg-primary-600 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1 shadow-sm">
                            @friendRequestsCount
                        </div>
                    }
                </div>
                <span class="@GetLabelClasses("/friends") text-xs font-medium leading-none">Friends</span>
            </button>

            <!-- Settings Tab -->
            <button @onclick='() => NavigateToTab("/settings")'
                    class="@GetTabClasses("/settings") flex-1 flex flex-col items-center justify-center py-2 px-2 min-h-[52px] transition-all duration-200 rounded-lg"
                    aria-label="Settings">
                <!-- Settings Icon -->
                <div class="@GetIconClasses("/settings") mb-1">
                    @if (IsActiveTab("/settings"))
                    {
                        <!-- Active Settings Icon (Filled) -->
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                        </svg>
                    }
                    else
                    {
                        <!-- Inactive Settings Icon (Outline) -->
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                    }
                </div>
                <span class="@GetLabelClasses("/settings") text-xs font-medium leading-none">Settings</span>
            </button>

            <!-- Profile Tab -->
            <button @onclick='() => NavigateToTab("/profile")'
                    class="@GetTabClasses("/profile") flex-1 flex flex-col items-center justify-center py-2 px-2 min-h-[52px] transition-all duration-200 rounded-lg"
                    aria-label="Profile">
                <!-- Profile Icon -->
                <div class="@GetIconClasses("/profile") mb-1">
                    @if (IsActiveTab("/profile"))
                    {
                        <!-- Active Profile Icon (Filled) -->
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                    }
                    else
                    {
                        <!-- Inactive Profile Icon (Outline) -->
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                    }
                </div>
                <span class="@GetLabelClasses("/profile") text-xs font-medium leading-none">Profile</span>
            </button>
        </div>
    </div>
}
