@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.Friends
@using Microsoft.AspNetCore.Components.Forms 
@using Platform.Client.Services.Features.Friends
@using Platform.Framework.Core
@page "/friends/add"
@page "/friends/edit/{Id}"
@inherits FormBase<FriendFormBusinessObject, FriendFormViewModel, string, IFriendFormDataService>

<!-- Main Container - WhatsApp Style -->
<div class="min-h-screen bg-nothing-black-50 dark:bg-nothing-black-950">
    <!-- Header Section - WhatsApp Green -->
    <div class="bg-primary-500 dark:bg-nothing-black-800 shadow-sm">
        <div class="px-4 py-4">
            <div class="flex items-center space-x-4">
                <button @onclick="GoBack"
                        class="p-2 rounded-full hover:bg-white/20 transition-colors duration-200">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                </button>
                <div>
                    <h1 class="text-xl font-semibold text-white">Add Friend</h1>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Content - WhatsApp Style -->
    <div class="bg-white dark:bg-nothing-black-900 min-h-screen">
        @if (IsWorking)
        {
            <!-- Loading State - Minimalistic -->
            <div class="flex items-center justify-center py-16">
                <div class="flex items-center space-x-3">
                    <div class="animate-spin rounded-full h-6 w-6 border-2 border-primary-500 border-t-transparent"></div>
                    <span class="text-nothing-black-600 dark:text-nothing-black-300">Saving friend...</span>
                </div>
            </div>
        }
        else if (!string.IsNullOrEmpty(Error))
        {
            <!-- Error State - Minimalistic -->
            <div class="p-4">
                <div class="bg-nothing-red-50 dark:bg-nothing-red-900/20 border border-nothing-red-200 dark:border-nothing-red-800 rounded-lg p-4">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 text-nothing-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-nothing-red-800 dark:text-nothing-red-200">Error</h3>
                            <p class="mt-1 text-sm text-nothing-red-700 dark:text-nothing-red-300">@Error</p>
                        </div>
                    </div>
                </div>
            </div>
        }

        <!-- Friend Form - WhatsApp Style -->
        <div class="px-4 py-6">
            <EditForm Model="SelectedItem" OnValidSubmit="HandleFormSubmit">
                <DataAnnotationsValidator />

                <!-- Form Fields -->
                <div class="space-y-6">
                    <!-- Name Field -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-nothing-black-700 dark:text-nothing-black-300 mb-2">
                            Full Name <span class="text-nothing-red-500">*</span>
                        </label>
                        <InputText id="name" @bind-Value="SelectedItem!.NickName"
                                   placeholder="Enter friend's full name"
                                   class="block w-full px-4 py-3 border border-nothing-black-300 dark:border-nothing-black-600 rounded-lg bg-white dark:bg-nothing-black-800 text-nothing-black-900 dark:text-white placeholder-nothing-black-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base" />
                        <ValidationMessage For="() => SelectedItem!.NickName" class="mt-1 text-sm text-nothing-red-600 dark:text-nothing-red-400" />
                    </div>

                    <!-- Phone Field -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-nothing-black-700 dark:text-nothing-black-300 mb-2">Phone</label>
                        <InputText id="phone" @bind-Value="SelectedItem!.AuthCode"
                                   placeholder="Enter phone number"
                                   class="block w-full px-4 py-3 border border-nothing-black-300 dark:border-nothing-black-600 rounded-lg bg-white dark:bg-nothing-black-800 text-nothing-black-900 dark:text-white placeholder-nothing-black-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base" />
                        <ValidationMessage For="() => SelectedItem!.AuthCode" class="mt-1 text-sm text-nothing-red-600 dark:text-nothing-red-400" />
                    </div>
                </div>

                <!-- Validation Summary -->
                <ValidationSummary class="mt-6 p-4 bg-nothing-red-50 dark:bg-nothing-red-900/20 border border-nothing-red-200 dark:border-nothing-red-800 rounded-lg text-sm" />

                <!-- Form Actions - WhatsApp Style -->
                <div class="px-4 py-6 border-t border-nothing-black-200 dark:border-nothing-black-700 bg-nothing-black-50 dark:bg-nothing-black-800">
                    <div class="flex space-x-3">
                        <button type="button" @onclick="GoBack"
                                class="flex-1 px-6 py-3 border border-nothing-black-300 dark:border-nothing-black-600 rounded-lg text-base font-medium text-nothing-black-700 dark:text-nothing-black-300 bg-white dark:bg-nothing-black-700 hover:bg-nothing-black-50 dark:hover:bg-nothing-black-600 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-200">
                            Cancel
                        </button>
                        <button type="submit"
                                disabled="@IsWorking"
                                class="flex-1 px-6 py-3 border border-transparent rounded-lg text-base font-medium text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
                            @if (IsWorking)
                            {
                                <div class="flex items-center justify-center">
                                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span>Adding...</span>
                                </div>
                            }
                            else
                            {
                                <span>Add Friend</span>
                            }
                        </button>
                    </div>
                </div>
            </EditForm>
        </div>
    </div>
</div>
