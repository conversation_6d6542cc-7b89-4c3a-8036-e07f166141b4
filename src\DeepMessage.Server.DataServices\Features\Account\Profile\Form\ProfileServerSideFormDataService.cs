﻿using DeepMessage.Framework.Core;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Account;
namespace DeepMessage.Server.DataServices.Features.Account;
public class ProfileServerSideFormDataService : IProfileFormDataService
{

	private readonly AppDbContext _context;

	public ProfileServerSideFormDataService (AppDbContext context)
	{
		_context = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(ProfileFormBusinessObject formBusinessObject)
	{
		throw new NotImplementedException();
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<ProfileFormBusinessObject?> GetItemByIdAsync(string id)
	{
		throw new NotImplementedException();
	}
}
