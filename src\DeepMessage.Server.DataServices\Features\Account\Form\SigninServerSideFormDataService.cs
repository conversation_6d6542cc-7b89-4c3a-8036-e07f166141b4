﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Enums;
using DeepMessage.ServiceContracts.Features.Account;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
namespace DeepMessage.Server.DataServices.Features.Account;
public class SigninServerSideFormDataService : ISignInFormDataService
{

    private readonly JWTSettings jwtSettings;
    private readonly UserManager<ApplicationUser> userManager;
    private readonly AppDbContext context;

    public SigninServerSideFormDataService(IOptions<JWTSettings> jwtSettings,
        UserManager<ApplicationUser> userManager, AppDbContext context)
    {
        this.jwtSettings = jwtSettings.Value;
        this.userManager = userManager;
        this.context = context;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(SigninFormBusinessObject formBusinessObject)
    {
        ArgumentNullException.ThrowIfNull(formBusinessObject.PassKey);

        if (string.IsNullOrEmpty(formBusinessObject.NickName))
        { 
            if (context.AuthCodes.Any(x => x.Code == formBusinessObject.PassKey && x.AuthCodeStatus == AuthCodeStatus.Unused))
            {
                return "Register";
            }
            else if (context.Users.Any(x => x.UserName == formBusinessObject.PassKey))
            {
                return "Login";
            }
        }

        ArgumentNullException.ThrowIfNull(formBusinessObject.NickName);

        var user = await userManager.FindByNameAsync(formBusinessObject.NickName);
        if (user != null && await userManager.CheckPasswordAsync(user, formBusinessObject.PassKey))
        {
            var authClaims = new AuthorizationClaimsModel(CreateJwtToken(user),
                 string.Empty,
                 user.Id,
                 user.UserName,
                 user.Pub1,
                 user.Pub2);
            return JsonSerializer.Serialize(authClaims);
        }
        throw new Exception("Invalid NickName or PassKey");
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public Task<SigninFormBusinessObject> GetItemByIdAsync(string id)
    {
        throw new NotImplementedException();
    }

    private string CreateJwtToken(ApplicationUser user)
    {
        var claims = new Claim[]
        {
                new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.UniqueName, user.UserName!),
                new Claim(JwtRegisteredClaimNames.Name, user.UserName!),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(JwtRegisteredClaimNames.Exp, DateTime.UtcNow.AddMinutes(30).ToString()),
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.UserName!),

        };
        return GenerateAccessToken(claims, 10000);
    }


    private string GenerateAccessToken(Claim[] claims, int minutes)
    {
        var signingKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings.Key));
        var signingCredentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);
        var jwt = new JwtSecurityToken(
            signingCredentials: signingCredentials,
            claims: claims,
            notBefore: DateTime.UtcNow,
            expires: DateTime.UtcNow.AddMinutes(minutes),
            audience: jwtSettings.Audience,
            issuer: jwtSettings.Issuer
            );

        var token = new JwtSecurityTokenHandler().WriteToken(jwt);
        return token;
    }
}
