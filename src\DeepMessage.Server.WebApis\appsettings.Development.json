{
  "Tokens": {
    "PublicKey": "public.xml",
    "Key": "hf9s8adfiUHIwArd2d^fwSef^2$34c5r243$D",
    "Lifetime": "86400",
    "Issuer": "deepmessage.io",
    "Audience": "private"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "ConnectionStrings": {
    //"DefaultConnection": "Server=wooqlaw-dev-server.uksouth.cloudapp.azure.com;Initial Catalog=Briefly;Persist Security Info=False;User ID=wooqlaw-prod-admin;Password=*****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=True;Connection Timeout=30;"
    "DefaultConnection": "Server=(local);Initial Catalog=deepmessage-v4;Persist Security Info=False;User ID=sa;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=True;Connection Timeout=30;"
  },
  "RateLimiting": {
    "EnableRateLimiting": true,
    "PermitLimit": 100,
    "Window": "00:01:00",
    "ReplenishmentPeriod": "00:00:10",
    "TokensPerPeriod": 10
  },
  "Security": {
    "RequireHttps": true,
    "EnableCors": true,
    "AllowedOrigins": [ "https://localhost:3000", "https://yourdomain.com" ],
    "MaxRequestBodySize": ********,
    "EnableRequestSizeLimit": true
  },
  "SignalR": {
    "MaximumReceiveMessageSize": 32768,
    "StreamBufferCapacity": 10,
    "EnableDetailedErrors": true,
    "KeepAliveInterval": "00:00:15",
    "ClientTimeoutInterval": "00:00:30"
  },
  "Firebase": {
    "ServiceAccountPath": "REPLACE_WITH_ENVIRONMENT_VARIABLE_OR_KEY_VAULT"
  }
}
