﻿using KPlatform.Framework.Core;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using System.Reflection;
using System.Reflection.Metadata;
using System.Runtime.CompilerServices;

namespace Platform.Framework.Core
{
    public class FrameworkBaseComponent : ComponentBase
    {

        [Parameter]
        public string? ModalSize { get; set; }

        [Parameter]
        public bool DisplayDialogCross { get; set; } = true;

        [Parameter]
        public ModalDialogConfig? DialogConfig { get; set; }

        [Inject]
        public NavigationManager Navigation { get; set; } = null!;

        [Inject]
        public ILocalStorageService StorageService { get; set; } = null!;

        [Inject]
        public DialogService DialogService { get; set; } = null!;

        [Inject]
        public KtNotificationService NotificationService { get; set; } = null!;

        [Inject]
        public AlertService AlertService { get; set; } = null!;

        [Inject]
        protected IServiceScopeFactory ScopeFactory { get; set; } = null!;

        [Inject]
        public IJSRuntime JsRuntime { get; set; } = null!;

        [Inject]
        public AuthenticationStateProvider AuthStateProvider { get; set; } = null!;
   
        [Inject]
        public IAuthenticatedUser AuthenticatedUser { get; set; } = null!;
         
        protected override async Task OnInitializedAsync()
        { 

            if (AuthenticatedUser == null)
                throw new ArgumentNullException($"Authenticated user");

            ArgumentNullException.ThrowIfNull(NotificationService);
            ArgumentNullException.ThrowIfNull(AuthStateProvider);

            var state = await AuthStateProvider.GetAuthenticationStateAsync();
            var user = state?.User;

            if (user != null && user.Identity != null && user.Identity.IsAuthenticated)
            {
                AuthenticatedUser.UserId = user.GetUserId();
                AuthenticatedUser.Username = user.GetUserName(); 
                AuthenticatedUser.ProfileName = user.GetProfileName();
                AuthenticatedUser.ImageUrl = user.GetImage();
      
            }

            PubSub.Hub.Default.Subscribe<Tuple<string, string, dynamic>>((x) =>
            {
                if (x.Item1 == OperationId && x.Item2 == "Dialog Closed")
                {
                    DialogService_OnClose(x.Item3);
                }
            }); 
            await base.OnInitializedAsync();
        } 

        public string OperationId { get; set; } = Guid.NewGuid().ToString().ToLower();

        protected KeyValuePair<string, object?> P(object? value, [CallerArgumentExpression("value")] string? variableName = null)
        {
            if (variableName == null) throw new ArgumentNullException(nameof(variableName));
            return new KeyValuePair<string, object?>(variableName.Split('.').Last(), value);
        }
         

        [Inject]
        private ILogger<FrameworkBaseComponent>? Logger { get; set; }

        
        

        public static Type? GetTypeByFullName(string typeFullName)
        {
            // Loop through all assemblies in the current AppDomain
            foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies().Where(x => x.FullName.StartsWith("K")))
            {
                // Look for the type in each assembly
                var type = assembly.GetType(typeFullName);
                if (type != null)
                {
                    return type; // Return the type if found
                }
            }

            return null; // Return null if the type was not found
        } 

        /// <summary>
        ///  This is being used from public portal, will see it in 2nd phase.
        /// </summary>
        /// <typeparam name="TKey"></typeparam>
        /// <param name="dialogType"></param>
        /// <param name="assembly"></param>
        /// <param name="title"></param>
        /// <param name="id"></param>
        /// <param name="parameters"></param>
        protected void ShowCenterDialog2<TKey>(string dialogType, Assembly? assembly, string? title, TKey? id, params List<KeyValuePair<string, object?>> parameters)
        {
            if (!string.IsNullOrEmpty(dialogType))
            {
                // Get the type of the component based on its name
                var componentType = assembly == null ? GetTypeByFullName(dialogType) : assembly.GetType(dialogType);

                if (componentType != null && typeof(ComponentBase).IsAssignableFrom(componentType))
                {
                    ShowDialog(componentType, title, id, Size.Xl4, Position_.Center, true, parameters);

                }
                else
                {
                    // Handle the case when the component type is not found or not a Blazor component
                    // For example, display an error message
                    Console.WriteLine($"Component '{componentType}' not found or not a Blazor component.");
                }
            }
        }

        public enum Size
        { 
            Sm,
            Md,
            Xl,
            Xl2,
            Xl3,
            Xl4,
            Xl5,
            Xl6,
            Xl7,
            Xl8,
        }

        public enum Position_
        {
            None = 0,
            Right,
            Center
        }

        protected void ShowDialog<T>(string title, object? id, Size dialogSize = Size.Xl,
            Position_ position = Position_.Right, bool showCrossIcon = true,
            params List<KeyValuePair<string, object?>> parameters) where T : ComponentBase
        {
            ShowDialog(typeof(T), title, id, dialogSize, position, showCrossIcon, parameters);
        }

    
        protected void ShowDialog(Type dialogType, string title, object? id, Size dialogSize = Size.Xl,
           Position_ position = Position_.Right, bool showCrossIcon = true,
           params List<KeyValuePair<string, object?>> parameters)
        {

            var config = new ModalDialogConfig()
            {
                Component = dialogType,
                Title = title,
                ShowCrossIcon = DisplayDialogCross,
            };

            var parameters_ = parameters.ToDictionary();
            parameters_.Add("DialogConfig", config);
            parameters_.Add("Id", id);
            parameters_.Add("OperationId", OperationId);

            if (DialogService == null)
                throw new Exception("Dialog service is not initialized");

            switch (position)
            {
                case Position_.Center:
                    config.PositionClasses = "justify-center h-auto";
                    config.DialogContainerClasses = "grid place-items-center";
                    break;
                case Position_.Right:
                    config.PositionClasses = "justify-end min-h-[calc(100%-0.5rem)] h-[calc(100%-0.5rem)]";
                    config.DialogContainerClasses = "flex justify-end";
                    break;
            }
            switch (dialogSize)
            {
                case Size.Sm:
                    config.SizeClasses = "md:max-w-sm";
                    break;
                case Size.Md:
                    config.SizeClasses = "md:max-w-md";
                    break;
                case Size.Xl:
                    config.SizeClasses = "md:max-w-xl";
                    break;
                case Size.Xl2:
                    config.SizeClasses = "md:max-w-2xl";
                    break;
                case Size.Xl3:
                    config.SizeClasses = "md:max-w-3xl";
                    break;
                case Size.Xl4:
                    config.SizeClasses = "md:max-w-4xl";
                    break;
                case Size.Xl5:
                    config.SizeClasses = "md:max-w-5xl";
                    break;
                case Size.Xl6:
                    config.SizeClasses = "md:max-w-6xl";
                    break;
                case Size.Xl7:
                    config.SizeClasses = "md:max-w-7xl";
                    break;
                case Size.Xl8:
                    config.SizeClasses = "md:max-w-screen-2xl";
                    break;
            }

            config.Parameters = parameters_!;
            DialogService.ShowDialogAsync(config);
            StateHasChanged();

        }
         
          
        public virtual void DialogService_OnClose(dynamic obj)
        {
            Console.WriteLine("Dialog closed");
        }

        protected async Task CloseDialog()
        {
            if (DialogConfig != null)
            {
                //await JsRuntime.InvokeVoidAsync("closeDialog", DialogConfig.Id);
                //await Task.Delay(50);
                DialogService.Dialogs.Remove(DialogConfig);
            }
        }

    }
}
