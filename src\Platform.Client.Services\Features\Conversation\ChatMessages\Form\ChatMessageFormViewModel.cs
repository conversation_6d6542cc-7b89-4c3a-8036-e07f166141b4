﻿using DeepMessage.Framework.Core;
using System.ComponentModel.DataAnnotations;
namespace Platform.Client.Services.Features.Conversation;
public class ChatMessageFormViewModel : ObservableBase, IValidateable
{
    [Required]
    public string? ConversationId { get; set; }

    private string? _content;

    [Required]
    public string? Content
    {
        get { return _content; }
        set
        {
            SetField(ref _content, value);
        }
    }



    public byte ContentType { get; set; }

    public void Validate()
    {
        if (string.IsNullOrEmpty(Content))
        {
            throw new ValidationException("Message is required.");
        }
        if (string.IsNullOrEmpty(ConversationId))
        {
            throw new ValidationException("ConversationId is required.");
        }
    }
}
