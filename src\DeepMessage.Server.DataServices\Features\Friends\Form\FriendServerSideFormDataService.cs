﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using System.Text.Json;
namespace DeepMessage.Server.DataServices.Features.Friends;
public class FriendServerSideFormDataService : IFriendFormDataService
{

    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public FriendServerSideFormDataService(AppDbContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(FriendFormBusinessObject formBusinessObject)
    {
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        ArgumentNullException.ThrowIfNull(userId, "User not found");

       var authCode = await _context.AuthCodes.FirstOrDefaultAsync(x => x.Code == formBusinessObject.AuthCode && 
                x.AuthCodeStatus == ServiceContracts.Enums.AuthCodeStatus.Unused);

        if (authCode == null)
        {
            throw new InvalidOperationException("Invalid Code");
        }
        var friend = _context.Friendships.FirstOrDefault(x => x.UserId == userId && x.FriendId == authCode.CreatedBy);
        if (friend != null)
        {
            throw new InvalidOperationException("Friend already exists");
        }
        var friendship = new Friendship
        {
            UserId = userId,
            FriendId = authCode.CreatedBy,
            Id = Guid.NewGuid().ToString(),
            CreatedAt = DateTime.UtcNow,
            Name = formBusinessObject.NickName,

        };
        _context.Friendships.Add(friendship);
        authCode.AuthCodeStatus = ServiceContracts.Enums.AuthCodeStatus.Consumed;
        authCode.ConsumedAt = DateTime.UtcNow;
        authCode.ConsumedByIp = contextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString();
        await _context.SaveChangesAsync();
        //todo: intimate code owner
        return JsonSerializer.Serialize(friendship);

    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public Task<FriendFormBusinessObject> GetItemByIdAsync(string id)
    {
        throw new NotImplementedException();
    }
}
