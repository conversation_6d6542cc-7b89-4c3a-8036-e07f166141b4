﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using System.Text.Json;
using Microsoft.AspNetCore.Identity;
using System.Security;
namespace DeepMessage.Server.DataServices.Features.Friends;
public class FriendServerSideFormDataService : IFriendFormDataService
{

    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor contextAccessor;
    private readonly UserManager<ApplicationUser> _userManager;

    public FriendServerSideFormDataService(AppDbContext context, IHttpContextAccessor contextAccessor, UserManager<ApplicationUser> userManager)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
        _userManager = userManager;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(FriendFormBusinessObject formBusinessObject)
    {
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        ArgumentNullException.ThrowIfNull(userId, "User not found");

       var authCode = await _context.AuthCodes.FirstOrDefaultAsync(x => x.Code == formBusinessObject.AuthCode && 
                x.AuthCodeStatus == ServiceContracts.Enums.AuthCodeStatus.Unused);

        if (authCode == null)
        {
            throw new InvalidOperationException("Invalid activation Code");
        }
        var friend = _context.Friendships.FirstOrDefault(x => x.UserId == userId && x.FriendId == authCode.CreatedBy);
        if (friend != null)
        {
            throw new InvalidOperationException("Friend already exists");
        }
        // Retrieve friend's public key from ApplicationUser
        var friendUser = await _userManager.FindByIdAsync(authCode.CreatedBy);
        if (friendUser == null)
        {
            throw new InvalidOperationException("Friend user not found");
        }

        // Security validation: Ensure we only share public keys, never private keys
        if (string.IsNullOrEmpty(friendUser.Pub1))
        {
            throw new InvalidOperationException("Friend's public key (Pub1) is not available");
        }

        // Additional security check: Verify we're not accidentally exposing private key
        if (friendUser.Pub1.Contains("PRIVATE") || friendUser.Pub1.Contains("-----BEGIN RSA PRIVATE KEY-----"))
        {
            throw new SecurityException("Security violation: Attempted to share private key data");
        }

        var friendship = new Friendship
        {
            UserId = userId,
            FriendId = authCode.CreatedBy,
            Id = Guid.NewGuid().ToString(),
            CreatedAt = DateTime.UtcNow,
            Name = formBusinessObject.NickName,
        };

        _context.Friendships.Add(friendship);
        authCode.AuthCodeStatus = ServiceContracts.Enums.AuthCodeStatus.Consumed;
        authCode.ConsumedAt = DateTime.UtcNow;
        authCode.ConsumedByIp = contextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString();
        await _context.SaveChangesAsync();

        // Create structured response with friend's public key
        var response = new FriendFormResponseDto
        {
            Id = friendship.Id,
            FriendId = friendship.FriendId,
            Username = friendUser.UserName ?? string.Empty,
            Name = friendship.Name,
            Pub1 = friendUser.Pub1, // Friend's RSA public key for encryption
            DisplayPictureUrl = friendship.DisplayPictureUrl,
            CreatedAt = friendship.CreatedAt
        };

        return JsonSerializer.Serialize(response);

    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public Task<FriendFormBusinessObject> GetItemByIdAsync(string id)
    {
        throw new NotImplementedException();
    }
}
