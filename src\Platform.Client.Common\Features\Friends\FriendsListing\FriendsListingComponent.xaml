﻿<?xml version="1.0" encoding="utf-8" ?>
<local:FriendsListingViewBase
    x:Class="Platform.Client.Common.Features.Friends.FriendsListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Friends"
    xmlns:friends="clr-namespace:Platform.Client.Services.Features.Friends;assembly=Platform.Client.Services"
    Title="Friends List"
    x:DataType="local:FriendsListingViewBase"
    BackgroundColor="{StaticResource Gray700}"
    Shell.TitleColor="White">
    <ContentPage.ToolbarItems>
        <ToolbarItem Clicked="ToolbarItem_Clicked" IconImageSource="plus_light.svg" />
    </ContentPage.ToolbarItems>
    <Border BackgroundColor="#F4F4F5" StrokeThickness="0">
        <Border.StrokeShape>
            <RoundRectangle CornerRadius="16,16,0,0" />
        </Border.StrokeShape>
        <CollectionView BackgroundColor="Transparent" ItemsSource="{Binding Items}">
            <CollectionView.ItemTemplate>
                <DataTemplate x:DataType="friends:FriendsListingViewModel">
                    <Grid Padding="12" ColumnSpacing="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="48" />
                        </Grid.ColumnDefinitions>
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type local:FriendsListingView}}, Path=BindingContext.MessageTappedCommand}" CommandParameter="{Binding .}" />
                        </Grid.GestureRecognizers>
                        <Border
                            Grid.Column="0"
                            BackgroundColor="{AppThemeBinding Light=#F2F2F2,
                                                              Dark=#1A1A1A}"
                            HeightRequest="50"
                            WidthRequest="50">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="45" />
                            </Border.StrokeShape>
                            <Image
                                Aspect="AspectFill"
                                HeightRequest="50"
                                Source="{Binding Avatar}"
                                WidthRequest="50" />
                        </Border>

                        <StackLayout Grid.Column="1" Spacing="2">
                            <Label
                                FontAttributes="Bold"
                                FontSize="16"
                                Text="{Binding Name}"
                                TextColor="{AppThemeBinding Light=Black,
                                                            Dark=White}" />
                            <Label
                                FontSize="14"
                                LineBreakMode="TailTruncation"
                                MaxLines="1"
                                Text="{Binding Status}"
                                TextColor="{AppThemeBinding Light=Gray,
                                                            Dark=LightGray}" />
                        </StackLayout>

                        <ImageButton
                            Grid.Column="2"
                            Margin="0,0,0,16"
                            MaximumHeightRequest="20"
                            MaximumWidthRequest="20"
                            Source="message_lines_light.svg"
                            VerticalOptions="Center" />

                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
    </Border>
</local:FriendsListingViewBase>
