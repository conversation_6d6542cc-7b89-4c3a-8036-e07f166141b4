﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.MauiApp.Helpers;
using DeepMessage.ServiceContracts.Features.Home;
using DeepMessage.Client.Common.Data;
using DeepMessage.Cient.Common.Data;
using DeepMessage.ServiceContracts.Features.Friends;

namespace Platform.Client.Services.Features.Home;
public class NewsOfflineListingDataService : ClientSideListingDataService<NewsListingBusinessObject, NewsFilterBusinessObject>,
            INewsListingDataService
{

    private readonly AppDbContext _context;

    public NewsOfflineListingDataService(AppDbContext context)
    {
        _context = context;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public override IQueryable<NewsListingBusinessObject> GetQuery(NewsFilterBusinessObject filterBusinessObject)
    {
        var query = _context.NewsItems.AsQueryable();

        // Apply search filter if SearchKey is provided
        if (!string.IsNullOrWhiteSpace(filterBusinessObject.SearchKey))
        {
            var searchTerm = filterBusinessObject.SearchKey.ToLower();
            query = query.Where(item =>
                item.Title.ToLower().Contains(searchTerm) ||
                item.Description.ToLower().Contains(searchTerm)
            );
        }

        return query
            .OrderByDescending(x => x.PubDate)
            .Select(x => new NewsListingBusinessObject
            {
                Title = x.Title,
                Description = x.Description,
                Link = x.Link,
                PubDate = x.PubDate
            });
    }
}
