@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.Conversation 
@using DeepMessage.ServiceContracts.Enums
@using Platform.Client.Services.Features.Conversation
@page "/chat/{ConversationId}"
@inherits ListingBase<ChatMessagesListingViewModel, ChatMessagesListingBusinessObject, ChatMessagesFilterViewModel, ChatMessagesFilterBusinessObject, IChatMessagesListingDataService>
@layout Platform.Razor.Components.EmptyLayout

<!-- Main Chat Container - WhatsApp Style -->
<div class="flex flex-col h-screen">
    <div class="bg-nothing-black-900 px-4 py-3">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <!-- Back Button -->
                <button @onclick="GoBack"
                        class="p-2 text-white rounded-full">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                </button>

                <!-- Participant Info -->
                <div class="flex items-center space-x-3">
                    @if (!string.IsNullOrEmpty(ParticipantAvatar))
                    {
                        <img src="@ParticipantAvatar" alt="@ParticipantName"
                             class="w-10 h-10 rounded-full object-cover" />
                    }
                    else
                    {
                        <div class="w-10 h-10 rounded-full bg-nothing-black-300 dark:bg-nothing-black-600 flex items-center justify-center">
                            <span class="text-white font-medium text-lg">@GetInitials(ParticipantName)</span>
                        </div>
                    }
                    <div>
                        <h1 class="text-lg font-semibold text-white">@ParticipantName</h1>
                        <p class="text-sm text-white/80">Online</p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center space-x-1">
                <button @onclick="ToggleSearch"
                        class="p-2 text-gray-200 rounded-full ">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>
                <button class="p-2 text-gray-200 rounded-full">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Search Bar (Hidden by default) -->
        @if (showSearch)
        {
            <div class="mt-3 relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-4 w-4 text-nothing-black-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <input type="text"
                       @bind="FilterViewModel.SearchText"
                       @bind:event="oninput"
                       @onkeyup="OnSearchKeyUp"
                       placeholder="Search messages..."
                       class="w-full pl-10 pr-3 py-2 bg-white dark:bg-nothing-black-700 border-0 rounded-lg text-nothing-black-900 dark:text-white placeholder-nothing-black-400 focus:outline-none focus:ring-2 focus:ring-whatsapp-green-300 dark:focus:ring-nothing-black-500 text-sm" />
            </div>
        }
    </div>

    <!-- Messages Container - WhatsApp Style Chat Bubbles -->
    <div class="flex flex-col h-screen items-start gap-5 px-4 pt-4 w-full bg-neutral-200 rounded-t-xl w-full flex-1 overflow-y-auto" @ref="messagesDiv">
        @if (IsWorking)
        {
            <!-- Loading State -->
            <div class="flex items-center justify-center py-16 w-full">
                <div class="flex items-center space-x-3">
                    <div class="animate-spin rounded-full h-6 w-6 border-2 border-whatsapp-green-500 border-t-transparent"></div>
                    <span class="text-nothing-black-600 dark:text-nothing-black-300">Loading messages...</span>
                </div>
            </div>
        }
        else if (!string.IsNullOrEmpty(Error))
        {
            <!-- Error State -->
            <div class="p-4 w-full">
                <div class="bg-critical-red-50 dark:bg-critical-red-900/20 border border-critical-red-200 dark:border-critical-red-800 rounded-lg p-4">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 text-critical-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-critical-red-800 dark:text-critical-red-200">Error loading messages</h3>
                            <p class="mt-1 text-sm text-critical-red-700 dark:text-critical-red-300">@Error</p>
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (Items?.Count == 0)
        {
            <!-- Empty State -->
            <div class="text-center py-16 px-4 w-full">
                <svg class="mx-auto h-16 w-16 text-nothing-black-300 dark:text-nothing-black-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
                </svg>
                <h3 class="mt-6 text-lg font-medium text-nothing-black-900 dark:text-white">No messages yet</h3>
                <p class="mt-2 text-sm text-nothing-black-500 dark:text-nothing-black-400 max-w-sm mx-auto">
                    Start the conversation by sending your first message!
                </p>
            </div>
        }
        else
        {
            <!-- Messages List - WhatsApp Style Bubbles -->
            @foreach (var message in Items)
            {
                @if (!message.IsIncoming)
                {
                    <!-- Outgoing Message (Right side) -->
                    <div class="flex flex-col items-end gap-1 pl-7 w-full">
                        <div class="flex items-start flex-row-reverse">
                            <div class="w-1 h-2 bg-zinc-300 border-t border-white rounded-br-[100px]"></div>
                            <div class="flex items-center gap-2.5 p-2.5 border-t border-white bg-zinc-300 font-normal text-base text-zinc-900 rounded-md rounded-tr-none max-w-xs lg:max-w-md">
                                @message.Content
                            </div>
                        </div>
                        <div class="flex items-center justify-end gap-2.5 pl-2 w-full">
                            @if (message.DeliveryStatus == DeliveryStatus.DeliveredToEndUser)
                            {
                                <span>
                                    <svg class="w-4 h-4 text-whatsapp-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                    </svg>
                                </span>
                            }
                            <span class="font-medium text-xs text-zinc-500">
                                @GetFormattedTime(message.Timestamp)
                            </span>
                        </div>
                    </div>
                }
                else
                {
                    <!-- Incoming Message (Left side) -->
                    <div class="flex flex-col items-start gap-1 pr-7 w-full ">
                        <div class="flex items-start">
                            <div class="w-1 h-2 bg-zinc-100 border-t border-white rounded-bl-[100px]"></div>
                            <div class="flex items-center gap-2.5 p-2.5 border-t border-white bg-zinc-100 font-normal text-base text-zinc-900 rounded-md rounded-tl-none max-w-xs lg:max-w-md">
                                @message.Content
                            </div>
                        </div>
                        <div class="flex items-center justify-end gap-2.5 pl-2 w-full">
                            <span class="font-medium text-xs text-zinc-500">
                                @GetFormattedTime(message.Timestamp)
                            </span>
                        </div>
                    </div>
                }
            }
        }
    </div>

    <!-- Message Input Form -->
    <div class="bg-white dark:bg-nothing-black-900 border-t border-nothing-black-200 dark:border-nothing-black-700 p-4">
        <Platform.Razor.Features.Chat.Messages.MessageForm ConversationId="@ConversationId" OnMessageSent="OnMessageSent" />
    </div>
</div>
