﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Account;
namespace ModelFury.Briefly.MobileApp.Features.Account;
public class SigninClientSideFormDataService : ISignInFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public SigninClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(SigninFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/SigninForm/Save", formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<SigninFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<SigninFormBusinessObject>($"api/SigninForm/GetItemById?id=" + id);
	}
}
