using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Platform.Framework.Core;
using Platform.Razor.Features.Authentication.Captcha;
using System.ComponentModel;
using System.Security.Claims;
using System.Text.RegularExpressions;
using System.Timers;

namespace Platform.Razor.Features.Home.Listing
{
    public partial class NewsListing
    {

        private System.Timers.Timer? _searchTimer;
        private const int SearchDelayMs = 500; // Debounce search for 500ms

        private string stealthModeCode = "***";

        protected override async Task OnInitializedAsync()
        {
            // Initialize the search timer for debounced search
            _searchTimer = new System.Timers.Timer(SearchDelayMs);
            _searchTimer.Elapsed += OnSearchTimerElapsed;
            _searchTimer.AutoReset = false;

            await LoadStealthModeSettings();

            await ActivateStealthMode();

            await base.OnInitializedAsync();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                // Auto-focus the search input and show keyboard for quick stealth code entry
                await Task.Delay(100); // Small delay to ensure DOM is ready
                await JsRuntime.InvokeVoidAsync("eval", "document.getElementById('newsSearchInput')?.focus()");
            }
            await base.OnAfterRenderAsync(firstRender);
        }

        /// <summary>
        /// Loads stealth mode settings from storage
        /// </summary>
        private async Task LoadStealthModeSettings()
        {
            try
            {
                //var stealthEnabled = await StorageService.GetValue("ask_code_on_cold");

                // Load custom stealth mode code
                var customCode = await StorageService.GetValue("stealth_mode_code");
                if (!string.IsNullOrEmpty(customCode))
                {
                    stealthModeCode = customCode;
                }
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "Error loading stealth mode settings");

                stealthModeCode = "***";
            }
        }



        protected override async void FilterViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(FilterViewModel.SearchKey))
            {
                if ((FilterViewModel.SearchKey != "***" || FilterViewModel.SearchKey != "###")
                    && FilterViewModel.SearchKey == stealthModeCode)
                {
                    await ActivateStealthMode();

                    StateHasChanged();
                    return;
                }
            }
        }


        /// <summary>
        /// Activates stealth mode and navigates to captcha screen
        /// </summary>
        private async Task ActivateStealthMode()
        {
            try
            {
                // Store the stealth activation timestamp
                var userName = await StorageService.GetValue(ClaimTypes.NameIdentifier);
                var caption = string.IsNullOrEmpty(userName) ? "Activate your News App" : "Prove You're not a Robot";
                ShowDialog<FakeCaptchaScreen>(caption, null, Size.Md, Position_.Center, true, P(true, "KeepAlive"));
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error activating stealth mode");
                Error = "Unable to activate stealth mode. Please try again.";
            }
        }

        /// <summary>
        /// Timer elapsed event for debounced search
        /// </summary>
        private void OnSearchTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            _ = InvokeAsync(async () =>
            {
                await LoadItems();
                StateHasChanged();
            });
        }

        /// <summary>
        /// Clears the search text and refreshes the list
        /// </summary>
        private async Task ClearSearch()
        { 
            FilterViewModel.SearchKey = string.Empty;
            await LoadItems();
        }

        /// <summary>
        /// Opens an article in a new tab/window
        /// </summary>
        private async Task OpenArticle(string? url)
        {
            if (!string.IsNullOrEmpty(url))
            {
                await JsRuntime.InvokeVoidAsync("open", url, "_blank");
            }
        }

        /// <summary>
        /// Cleans HTML tags and entities from description text
        /// </summary>
        private string GetCleanDescription(string? description)
        {
            if (string.IsNullOrEmpty(description))
                return string.Empty;

            // Remove HTML tags
            var cleanText = Regex.Replace(description, "<.*?>", string.Empty);

            // Decode common HTML entities
            cleanText = cleanText
                .Replace("&amp;", "&")
                .Replace("&lt;", "<")
                .Replace("&gt;", ">")
                .Replace("&quot;", "\"")
                .Replace("&#39;", "'")
                .Replace("&nbsp;", " ");

            // Trim and limit length for better display
            cleanText = cleanText.Trim();
            if (cleanText.Length > 200)
            {
                cleanText = cleanText.Substring(0, 200) + "...";
            }

            return cleanText;
        }

        /// <summary>
        /// Refreshes the news items
        /// </summary>
        private async Task RefreshItems()
        {
            //Error = string.Empty;
            await LoadItems();
        }

        //protected override void Dispose()
        //{
        //    _searchTimer?.Stop();
        //    _searchTimer?.Dispose();
        //    base.Dispose();
        //}
    }
}
