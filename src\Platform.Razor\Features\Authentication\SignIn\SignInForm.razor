@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.Account
@using ModelFury.Briefly.MobileApp.Features.Account
@using Platform.Framework.Core
@page "/login"
@inherits FormBase<SigninFormBusinessObject, SignInFormViewModel, string, ISignInFormDataService>

<!-- Main Container with Nothing Phone Black Background -->
<div class="min-h-screen bg-gradient-nothing-subtle dark:bg-gradient-nothing-dark flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Header with Nothing Phone Black Icon -->
        <div class="text-center">
            <div class="mx-auto h-16 w-16 bg-gradient-nothing rounded-full flex items-center justify-center mb-6 shadow-lg">
                <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
            </div>
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Welcome back</h2>
            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                Sign in to your account to continue
            </p>
        </div>

        <!-- Sign In Form -->
        <div class="bg-white dark:bg-gray-800 shadow-xl rounded-lg px-8 py-8 space-y-6">
            <EditForm Model="SelectedItem" OnValidSubmit="HandleFormSubmit">
                <DataAnnotationsValidator />
                <ValidationSummary />
                <!-- Email/Username Field -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        NickName
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                            </svg>
                        </div>
                        <InputText @bind-Value="SelectedItem.NickName"
                                 
                                   placeholder="Enter your email or username"
                                   class="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-nothing-black-500 focus:border-nothing-black-500 transition-colors duration-200" />
                    </div>
                    <ValidationMessage For="@(() => SelectedItem!.NickName)" class="mt-1 text-sm text-red-600 dark:text-red-400" />
                </div>

                <!-- Password Field -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Password
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <InputText @bind-Value="SelectedItem!.PassKey" 
                                   id="password"
                                   type="@(SelectedItem!.ShowPassword ? "text" : "password")"
                                   placeholder="Enter your password"
                                   class="block w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-nothing-black-500 focus:border-nothing-black-500 transition-colors duration-200" />
                        <button type="button" @onclick="TogglePasswordVisibility"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            @if (SelectedItem!.ShowPassword)
                            {
                                <svg class="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                </svg>
                            }
                            else
                            {
                                <svg class="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            }
                        </button>
                    </div>
                    <ValidationMessage For="@(() => SelectedItem!.PassKey)" class="mt-1 text-sm text-red-600 dark:text-red-400" />
                </div>
                <input type="hidden"  @bind-value=SelectedItem.DeviceString/>

                <!-- Sign In Button -->
                <div>
                    <button type="submit" 
                            disabled="@(IsWorking)"
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-nothing-red-500 hover:bg-nothing-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-nothing-red-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl">
                        @if (IsWorking)
                        {
                            <div class="spinner-red -ml-1 mr-3 h-5 w-5"></div>
                            <span>Signing in...</span>
                        }
                        else
                        {
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <svg class="h-5 w-5 text-white/80 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                            <span>Sign in</span>
                        }
                    </button>
                </div>

                <!-- Error Display -->
                @if (!string.IsNullOrEmpty(Error))
                {
                    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                        <div class="flex">
                            <svg class="h-5 w-5 text-red-600 dark:text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Sign in failed</h3>
                                <p class="mt-1 text-sm text-red-700 dark:text-red-300">@Error</p>
                            </div>
                        </div>
                    </div>
                }
            </EditForm>

           
          
        </div>

    </div>
</div>
