﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Services\**" />
    <Content Remove="Services\**" />
    <EmbeddedResource Remove="Services\**" />
    <None Remove="Services\**" />
  </ItemGroup>


  <ItemGroup>
    <SupportedPlatform Include="browser" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.4" />
	  <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.4" />
	  <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.4" />
	  <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.4" />
  </ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\DeepMessage.Framework.Core\Platform.Framework.Core.csproj" />
		<ProjectReference Include="..\DeepMessage.ServiceContracts\DeepMessage.ServiceContracts.csproj" />
		<ProjectReference Include="..\Platform.Client.Data\Platform.Client.Data.csproj" />
		<ProjectReference Include="..\Platform.Client.Services\Platform.Client.Services.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <Folder Include="Features\" />
	</ItemGroup>

</Project>
