﻿using ModelFury.Briefly.MobileApp.Features.Account;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Account;
using Microsoft.Extensions.DependencyInjection;
using MobileApp.MauiShared;
using System.Security.Claims;
using System.Text.Json;
using System.Windows.Input;
using Java.Time;
using Platform.Framework.Core;
namespace ModelFury.Briefly.MobileApp.Features.Account;
public class SignupFormViewBase : FormBaseMaui<SignupFormBusinessObject, SignupFormViewModel, string, ISignupFormDataService>
{
    public SignupFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }
}

[QueryProperty(nameof(AuthCode), "AuthCode")]
public partial class SignupFormView : SignupFormViewBase
{
    private string? authCode;
     
    public string? AuthCode
    {
        get => authCode;
        set => authCode = value;
    }
    public ICommand GoToSigninCommand () => new Command(async () =>
    {
        await Shell.Current.GoToAsync("//signin");
        await Navigation.PopToRootAsync();
    });

    public override Color TitleBarColor => Color.FromArgb("004f98");
     
    public SignupFormView(IServiceScopeFactory scopeFactory) : base(scopeFactory, null!)
    {
        InitializeComponent();
        BindingContext = this;
    }
     
    protected override Task<SignupFormViewModel> CreateSelectedItem()
    {
        return Task.FromResult(new SignupFormViewModel()
        {
            //AuthCode = authCode,
            DeviceString = $"{DeviceInfo.Manufacturer}-{DeviceInfo.Model}-{DeviceInfo.Platform}-{DeviceInfo.VersionString}"
        });
    }

    public override async Task OnAfterSaveAsync(string key)
    {
        var storageService = ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<ILocalStorageService>();

        var authClaims = JsonSerializer.Deserialize<AuthorizationClaimsModel>(key);
        await storageService.SetValue(authClaims.Token, "auth_token");
        await storageService.SetValue(authClaims.RefreshToken, "refresh_token");
        await storageService.SetValue(authClaims.UserId, ClaimTypes.NameIdentifier);
        await storageService.SetValue(authClaims.Username, ClaimTypes.Name);

        await Shell.Current.GoToAsync("//messages");
        await Navigation.PopToRootAsync();
    }
}
