﻿using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.ServiceContracts.Features.Friends;
using System.Security.Claims;
using Microsoft.AspNetCore.Http;
namespace DeepMessage.Server.DataServices.Features.Conversation;
public class ChatMessagesServerSideListingDataService : ServerSideListingDataService<ChatMessagesListingBusinessObject, ChatMessagesFilterBusinessObject>, IChatMessagesListingDataService
{

    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public ChatMessagesServerSideListingDataService(AppDbContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public override IQueryable<ChatMessagesListingBusinessObject> GetQuery(ChatMessagesFilterBusinessObject filterBusinessObject)
    {
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var query = (from m in _context.Messages
                     where m.ConversationId == filterBusinessObject.ConversationId
                     select new ChatMessagesListingBusinessObject
                     {
                         Id = m.Id,
                         Content = m.PlainContent,
                         IsIncoming = m.SenderId != userId,
                         Timestamp = m.CreatedAt,
                         DeliveryStatus = m.DeliveryStatus
                     });

        // Apply search filter if SearchKey is provided
        if (!string.IsNullOrWhiteSpace(filterBusinessObject.SearchKey))
        {
            var searchTerm = filterBusinessObject.SearchKey.ToLower();
            query = query.Where(message =>
                message.Content != null && message.Content.ToLower().Contains(searchTerm)
            );
        }

        // Order by timestamp (oldest first for chat messages)
        return query.OrderBy(message => message.Timestamp);
    }
}
