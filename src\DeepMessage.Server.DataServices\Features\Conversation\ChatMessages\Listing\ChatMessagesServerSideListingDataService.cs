﻿using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.ServiceContracts.Features.Friends;
using System.Security.Claims;
using Microsoft.AspNetCore.Http;
namespace DeepMessage.Server.DataServices.Features.Conversation;
public class ChatMessagesServerSideListingDataService : ServerSideListingDataService<ChatMessagesListingBusinessObject, ChatMessagesFilterBusinessObject>, IChatMessagesListingDataService
{

    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public ChatMessagesServerSideListingDataService(AppDbContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public override IQueryable<ChatMessagesListingBusinessObject> GetQuery(ChatMessagesFilterBusinessObject filterBusinessObject)
    {
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        // ✅ FIXED: Query MessageRecipient table for E2E encrypted messages
        var query = (from mr in _context.MessageRecipients
                     join m in _context.Messages on mr.MessageId equals m.Id
                     where m.ConversationId == filterBusinessObject.ConversationId
                           && mr.RecipientId == userId // Only get messages for current user
                     select new ChatMessagesListingBusinessObject
                     {
                         Id = m.Id,
                         Content = mr.EncryptedContent, // ✅ Return encrypted content for client-side decryption
                         IsIncoming = m.SenderId != userId,
                         Timestamp = m.CreatedAt,
                         DeliveryStatus = mr.DeliveryStatus // Use recipient-specific delivery status
                     });

        // Note: Search functionality disabled for encrypted content
        // Search would need to be implemented client-side after decryption
        if (!string.IsNullOrWhiteSpace(filterBusinessObject.SearchKey))
        {
            // For E2E encryption, search must be performed client-side after decryption
            // Server cannot search encrypted content
            // This could be enhanced by implementing client-side search or search indexes
        }

        // Order by timestamp (oldest first for chat messages)
        return query.OrderBy(message => message.Timestamp);
    }
}
