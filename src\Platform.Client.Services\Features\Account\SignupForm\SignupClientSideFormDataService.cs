﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Account;
using Platform.Client.Services.Services;
using Platform.Framework.Core;
using System.Text.Json;
using System.Security.Claims;

namespace ModelFury.Briefly.MobileApp.Features.Account;
public class SignupClientSideFormDataService : ISignupFormDataService
{
	private readonly BaseHttpClient _httpClient;
	private readonly IClientEncryptionService _encryptionService;
	private readonly ILocalStorageService _localStorageService;
	private readonly ISecureKeyManager _secureKeyManager;

	public SignupClientSideFormDataService(BaseHttpClient context, IClientEncryptionService encryptionService, ILocalStorageService localStorageService, ISecureKeyManager secureKeyManager)
	{
		_httpClient = context;
		_encryptionService = encryptionService;
		_localStorageService = localStorageService;
		_secureKeyManager = secureKeyManager;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(SignupFormBusinessObject formBusinessObject)
	{
		try
		{
			// Step 1: Generate RSA 2048-bit key pair on client-side
			var keyPair = await _encryptionService.GenerateRSAKeyPairAsync();

			// Step 2: Derive AES-256 key from PassKey using username as deterministic salt
			var aesKey = await _secureKeyManager.DeriveTemporaryAESKeyAsync(formBusinessObject.NickName!, formBusinessObject.PassKey!);

			// Step 3: Encrypt username with derived AES key for server authentication
			var encryptedUsername = await _encryptionService.EncryptWithAESAsync(formBusinessObject.NickName!, aesKey);

			// Step 4: Encrypt RSA private key with derived AES key
			var encryptedPrivateKey = await _encryptionService.EncryptRSAPrivateKeyAsync(keyPair.PrivateKeyPem, aesKey);

			// Step 5: Store only essential data locally (NO KEYS PERSISTED)
			await _localStorageService.SetValue(formBusinessObject.NickName!, ClaimTypes.Name);
			await _localStorageService.SetValue(encryptedUsername, ClaimTypes.Sid);
            await _localStorageService.SetValue(encryptedPrivateKey, "pub2e_");
			await _localStorageService.SetValue(keyPair.PublicKeyPem, "pub1o_");

            formBusinessObject.PassKey = encryptedUsername;
			formBusinessObject.Pub1 = keyPair.PublicKeyPem;
            formBusinessObject.Pub2 = encryptedPrivateKey;

            // Step 7: Send to server
            var response = await _httpClient.PostAsJsonAsync<string>($"api/SignupsForm/Save", formBusinessObject);

			// Step 8: Store user ID after successful server response (NO SENSITIVE DATA)
			if (!string.IsNullOrEmpty(response))
			{
				var authClaims = JsonSerializer.Deserialize<AuthorizationClaimsModel>(response);
				if (authClaims != null)
				{
					await _localStorageService.SetValue(authClaims.UserId, ClaimTypes.NameIdentifier);
					
					// Initialize secure key manager with the keys in memory
					await _secureKeyManager.DeriveAndStoreKeysAsync(formBusinessObject.NickName!, formBusinessObject.PassKey!);
				}
			}

			// Clear sensitive data from local variables
			Array.Clear(aesKey, 0, aesKey.Length);

			return response;
		}
		catch (Exception ex)
		{
			// Clear any potentially stored sensitive data on error
			_secureKeyManager.ClearKeys();
			throw new Exception($"Signup failed: {ex.Message}");
		}
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<SignupFormBusinessObject> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<SignupFormBusinessObject>($"api/SignupsForm/GetItemById?id=" + id);
	}
}
