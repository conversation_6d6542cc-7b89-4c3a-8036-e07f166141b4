using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Timers;
using DeepMessage.ServiceContracts.Features.Friends;
using Platform.Client.Services.Features.Friends;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using DeepMessage.Client.Common.Data;
using Microsoft.EntityFrameworkCore;
using DeepMessage.ServiceContracts.Features.Conversation;
using System.Threading.Tasks;
using System.Security.Claims;

namespace Platform.Razor.Features.Friends.Listing
{
    public partial class FriendsListing : IDisposable
    {
        [Inject] private ILogger<FriendsListing> ComponentLogger { get; set; } = null!;


        private System.Timers.Timer? _searchTimer;
        private const int SearchDelayMs = 500; // Debounce search for 500ms
        private static bool _isFirstLoad = true;

        protected override async Task OnInitializedAsync()
        {
            // Initialize the search timer for debounced search
            _searchTimer = new System.Timers.Timer(SearchDelayMs);
            _searchTimer.Elapsed += OnSearchTimerElapsed;
            _searchTimer.AutoReset = false;


            await base.OnInitializedAsync();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender && _isFirstLoad)
            {
                _isFirstLoad = false;
                await CheckAndPerformSync();
            }

            await base.OnAfterRenderAsync(firstRender);
        }

        protected override async Task ItemsLoaded(IFriendsListingDataService service)
        {
            if (Items.Count == 0)
            {
                var scope = ScopeFactory!.CreateAsyncScope();
                var friendsClientListingDataService = scope.ServiceProvider.GetRequiredKeyedService<IFriendsListingDataService>("client");

                var pagedItems = await friendsClientListingDataService.GetPaginatedItems(new FriendsFilterBusinessObject() { UsePagination = false });
                if (pagedItems == null)
                    throw new Exception($"Paginated items null from {typeof(FriendsFilterBusinessObject)}");
                Items = ConvertToListViewModel(pagedItems.Items);
                TotalPages = pagedItems.TotalPages;
                TotalRecords = pagedItems.TotalRows;

            }

        }

        /// <summary>
        /// Handles search input with debouncing
        /// </summary>
        private void OnSearchKeyUp()
        {
            // Reset and restart the timer for debounced search
            _searchTimer?.Stop();
            _searchTimer?.Start();
        }

        /// <summary>
        /// Timer elapsed event for debounced search
        /// </summary>
        private async void OnSearchTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            await InvokeAsync(async () =>
            {
                // Trigger search when timer elapses
                await LoadItems();
                StateHasChanged();
            });
        }

        /// <summary>
        /// Clears the search text and refreshes the list
        /// </summary>
        private async Task ClearSearch()
        {
            FilterViewModel.SearchText = string.Empty;
            await LoadItems();
        }

        /// <summary>
        /// Checks if synchronization is needed and performs it
        /// </summary>
        private async Task CheckAndPerformSync()
        {
            try
            {
                var userId = await StorageService?.GetValue(ClaimTypes.NameIdentifier) ?? string.Empty;
                if (string.IsNullOrEmpty(userId))
                {
                    ComponentLogger.LogWarning("No user ID found, skipping sync");
                    return;
                }


                using var scope = ScopeFactory.CreateScope();
                var friendsService = scope.ServiceProvider.GetRequiredService<FriendsClientSideListingDataService>();

                var filter = new FriendsFilterBusinessObject
                {
                    UsePagination = false, // Get all friends
                    RowsPerPage = 1000
                };

                var friendsData = await friendsService.GetPaginatedItems(filter);

                foreach (var friend in friendsData.Items)
                {
                    var localScope = ScopeFactory.CreateScope();
                    var context = localScope.ServiceProvider.GetRequiredService<AppDbContext>();
                    var friendship = await context.Friendships.FirstOrDefaultAsync(x => x.Id == friend.Id);
                    if (friendship == null)
                    {
                        friendship = new Friendship()
                        {
                            Id = friend.Id,
                            UserId = userId,
                            FriendId = friend.FriendId,
                            Name = friend.Name,
                            CreatedAt = DateTime.Now
                        };
                        context.Friendships.Add(friendship);
                        await context.SaveChangesAsync();
                    }
                }

            }
            catch (Exception ex)
            {
                ComponentLogger.LogError(ex, "Error during sync check and execution");
            }
        }



        /// <summary>
        /// Shows the add friend form
        /// </summary>
        private void ShowAddFriendForm()
        {
            Navigation?.NavigateTo("/friends/add");
        }

        /// <summary>
        /// Starts a chat with the selected friend
        /// </summary>
        private async Task StartChat(FriendsListingViewModel friend)
        {
            var scope = ScopeFactory!.CreateAsyncScope();
            var startChatFormDataService = scope.ServiceProvider.GetRequiredService<IStartChatFormDataService>();
            var chatId = await startChatFormDataService.SaveAsync(new StartChatFormBusinessObject()
            {
                FriendId = friend.FriendId
            });
            Navigation?.NavigateTo($"/chat/{chatId}");
        }

        /// <summary>
        /// Gets the initials from a name for avatar display
        /// </summary>
        private string GetInitials(string? name)
        {
            if (string.IsNullOrEmpty(name))
                return "?";

            var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 0)
                return "?";

            if (parts.Length == 1)
                return parts[0].Substring(0, Math.Min(2, parts[0].Length)).ToUpper();

            return $"{parts[0][0]}{parts[^1][0]}".ToUpper();
        }

        /// <summary>
        /// Refreshes the friends list
        /// </summary>
        private async Task RefreshItems()
        {
            Error = string.Empty;
            await LoadItems();
        }


        void IDisposable.Dispose()
        {

            _searchTimer?.Stop();
            _searchTimer?.Dispose();
        }
    }
}
