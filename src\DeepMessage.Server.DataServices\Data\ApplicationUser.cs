﻿using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace DeepMessage.Server.DataServices.Data
{
    public class ApplicationUser : IdentityUser
    {
        [MaxLength(4000)]
        public string Pub1 { get; set; } = string.Empty;

        [MaxLength(4000)]
        public string Pub2 { get; set; } = string.Empty;

        // Navigation
        public ICollection<ConversationParticipant> Conversations { get; set; } = new List<ConversationParticipant>();

        // This links to each user’s copy of the message (where encryption, read status is stored)
        public ICollection<MessageRecipient> MessageRecipients { get; set; } = new List<MessageRecipient>();

        // Navigation: friend requests (both as requestor and as target)
        public ICollection<FriendRequest> FriendRequestsSent { get; set; } = new List<FriendRequest>();
        public ICollection<FriendRequest> FriendRequestsReceived { get; set; } = new List<FriendRequest>();

        // Navigation: actual friendships
        public ICollection<Friendship> Friendships { get; set; } = new List<Friendship>();

    }
}
