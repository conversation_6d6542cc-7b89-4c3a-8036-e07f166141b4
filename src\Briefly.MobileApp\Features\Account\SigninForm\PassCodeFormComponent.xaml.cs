using DeepMessage.Client.Common.Data;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Account;
using DeepMessage.ServiceContracts.Features.Configurations;
using MobileApp.MauiShared;
using Platform.Framework.Core;
using Plugin.Firebase.CloudMessaging;
using System.ComponentModel;
using System.Security.Claims;
using System.Text.Json;

namespace ModelFury.Briefly.MobileApp.Features.Account;

public class PassCodeViewModelBase : FormBaseMaui<SigninFormBusinessObject, SigninFormViewModel, string, ISignInFormDataService>
{
    public PassCodeViewModelBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {

    }

}

public partial class PassCodeFormComponent : PassCodeViewModelBase
{
    public PassCodeFormComponent(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
        InitializeComponent(); 
        Box0.Focus();
        BindingContext = this;
    }
     


    /// <summary>
    /// Override form submission to implement offline/online fallback authentication
    /// </summary>
    public override async Task HandleFormSubmit(object sender, EventArgs e)
    {
        try
        {
            Logger.LogInformation("Form submitted");
            await MainThread.InvokeOnMainThreadAsync(() => IsBusy = true);
            await Task.Delay(100);
            using var scope = ScopeFactory.CreateScope();

            if (SelectedItem is IValidateable validateable)
            {
                validateable.Validate();
            }

            var businessModel = ConvertViewModelToBusinessModel(SelectedItem)
                ?? throw new InvalidDataException("Business model is null before calling SaveAsync");

            await BeforeSaveAsync();

            string authResult;

            try
            {
                // Step 1: Try offline authentication first (default service)
                var offlineService = scope.ServiceProvider.GetRequiredService<ISignInFormDataService>();
                authResult = await offlineService.SaveAsync(businessModel);
            }
            catch (Exception offlineEx)
            {
                // Step 2: Fallback to online authentication (backup service)
                try
                {
                    var onlineService = scope.ServiceProvider.GetKeyedService<ISignInFormDataService>("backup");
                    if (onlineService == null)
                        throw new InvalidOperationException("Backup authentication service not found");

                    authResult = await onlineService.SaveAsync(businessModel);
                }
                catch (Exception onlineEx)
                {
                    throw new Exception($"Authentication failed. Offline: {offlineEx.Message}. Online: {onlineEx.Message}");
                }
            }

            await OnAfterSaveAsync(authResult);
        }
        catch (Exception ex)
        {
            LogAndDisplayError(ex);
        }
        finally
        {
            await MainThread.InvokeOnMainThreadAsync(() => IsBusy = false);
        }
    }

    public override async Task OnAfterSaveAsync(string key)
    {
        if (key.Length < 20)
        {
            await Shell.Current.GoToAsync($"//signup?AuthCode={key}");
            await Navigation.PopToRootAsync();
        }
        else
        {
            using var scope = ScopeFactory.CreateScope();
            var localStorage = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
            var authClaims = JsonSerializer.Deserialize<AuthorizationClaimsModel>(key);
            await localStorage.SetValue(authClaims.Token, "auth_token");
            await localStorage.SetValue(authClaims.UserId, ClaimTypes.NameIdentifier);
            await localStorage.SetValue(authClaims.Username, ClaimTypes.Name);
 
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            var user = await context.ApplicationUsers.FindAsync(authClaims.UserId);
            if (user == null)
            {
                user = new ApplicationUser()
                {
                    Id = authClaims.UserId,
                    NickName = authClaims.Username,
                    Hash = "dummy",
                };
                context.ApplicationUsers.Add(user);
                await context.SaveChangesAsync();
            }

            var deviceId = await localStorage.GetValue("device_id");
            if (string.IsNullOrEmpty(deviceId))
            {
                deviceId = Guid.NewGuid().ToString();
                await localStorage.SetValue(deviceId, "device_id");
            }

            var deviceToken = await localStorage.GetValue("device_token");
            if (string.IsNullOrEmpty(deviceToken))
            {
                var fcm = scope.ServiceProvider.GetRequiredService<IFirebaseCloudMessaging>();
                deviceToken = await fcm.GetTokenAsync();
                await localStorage.SetValue(deviceToken, "device_token");
            }

            var deviceTokenRegistration = await localStorage.GetValue("device_token_registration");
            // if (string.IsNullOrEmpty(deviceTokenRegistration))
            {
                var deviceTokenFormDataService = scope.ServiceProvider.GetRequiredService<IDeviceTokenFormDataService>();
                await deviceTokenFormDataService.SaveAsync(new DeviceTokenFormBusinessObject()
                {
                    Id = deviceId,
                    DeviceToken = deviceToken,
                    DeviceName = $"{DeviceInfo.Manufacturer}-{DeviceInfo.Model}-{DeviceInfo.Platform}-{DeviceInfo.VersionString}",
                    Platform = DeviceInfo.Platform.ToString(),
                });

                await localStorage.SetValue("true", "device_token_registration");
            }

            await Shell.Current.GoToAsync("//messages");
            await Navigation.PopToRootAsync();
        }
    }

    public override Task BeforeSaveAsync()
    {
       
        return base.BeforeSaveAsync();
    }

    protected override async Task<SigninFormViewModel> CreateSelectedItem()
    {
        var scope = ScopeFactory.CreateScope();
        var localStorageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
        return new SigninFormViewModel()
        {
            NickName = await localStorageService.GetValue(ClaimTypes.Name),
            DeviceString = $"{DeviceInfo.Manufacturer}-{DeviceInfo.Model}-{DeviceInfo.Platform}-{DeviceInfo.VersionString}"
        };
    }
     

    private void ImageButton_Clicked(object sender, EventArgs e)
    {
        Navigation.PopModalAsync();
    }
}