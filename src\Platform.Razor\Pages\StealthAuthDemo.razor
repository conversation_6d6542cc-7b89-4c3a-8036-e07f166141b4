@page "/stealth-demo"
@using Platform.Razor.Features.Home.Listing
@using Platform.Razor.Features.Authentication.Captcha
@using Platform.Razor.Features.Authentication.SignUp

<PageTitle>Stealth Authentication System Demo</PageTitle>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Demo Header -->
    <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    Stealth Authentication System
                </h1>
                <p class="text-lg text-gray-600 dark:text-gray-400 max-w-4xl mx-auto">
                    A sophisticated stealth-mode authentication system that provides hidden authentication through a news interface.
                    Features dual-purpose auth codes, device registration detection, and realistic captcha simulation.
                </p>
            </div>

            <!-- Component Navigation -->
            <div class="mt-12 flex flex-wrap justify-center gap-4">
                <button @onclick='() => ShowComponent("flow")'
                        class="@(currentComponent == "flow" ? "bg-blue-600 text-white" : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600") px-6 py-3 rounded-lg font-medium transition-colors duration-200 hover:bg-blue-700 hover:text-white">
                    Authentication Flow
                </button>
                <button @onclick='() => ShowComponent("news")'
                        class="@(currentComponent == "news" ? "bg-blue-600 text-white" : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600") px-6 py-3 rounded-lg font-medium transition-colors duration-200 hover:bg-blue-700 hover:text-white">
                    News Interface
                </button>
                <button @onclick='() => ShowComponent("captcha")'
                        class="@(currentComponent == "captcha" ? "bg-blue-600 text-white" : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600") px-6 py-3 rounded-lg font-medium transition-colors duration-200 hover:bg-blue-700 hover:text-white">
                    Captcha Screen
                </button>
                <button @onclick='() => ShowComponent("signup")'
                        class="@(currentComponent == "signup" ? "bg-blue-600 text-white" : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600") px-6 py-3 rounded-lg font-medium transition-colors duration-200 hover:bg-blue-700 hover:text-white">
                    Stealth Signup
                </button>
                <button @onclick='() => ShowComponent("architecture")'
                        class="@(currentComponent == "architecture" ? "bg-blue-600 text-white" : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600") px-6 py-3 rounded-lg font-medium transition-colors duration-200 hover:bg-blue-700 hover:text-white">
                    System Architecture
                </button>
            </div>
        </div>
    </div>

    <!-- Component Display -->
    <div class="py-8">
        @if (currentComponent == "flow")
        {
            <!-- Authentication Flow Diagram -->
            <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">Stealth Authentication Flow</h2>

                    <!-- Flow Steps -->
                    <div class="space-y-8">
                        <!-- Step 1: News Interface -->
                        <div class="flex items-center space-x-6">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                                    <span class="text-blue-600 dark:text-blue-400 font-bold">1</span>
                                </div>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">News Interface Entry</h3>
                                <p class="text-gray-600 dark:text-gray-400">Users start at the news screen which appears as a standard news aggregation site</p>
                                <div class="mt-2 text-sm text-blue-600 dark:text-blue-400">
                                    Route: <code>/</code> or <code>/news</code>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Arrow -->
                        <div class="flex justify-center">
                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                            </svg>
                        </div>

                        <!-- Step 2: Stealth Activation -->
                        <div class="flex items-center space-x-6">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                                    <span class="text-orange-600 dark:text-orange-400 font-bold">2</span>
                                </div>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Stealth Mode Activation</h3>
                                <p class="text-gray-600 dark:text-gray-400">User enters stealth code (default: "***") in the search field</p>
                                <div class="mt-2 text-sm text-orange-600 dark:text-orange-400">
                                    Trigger: Search field input detection
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Arrow -->
                        <div class="flex justify-center">
                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                            </svg>
                        </div>

                        <!-- Step 3: Fake Captcha -->
                        <div class="flex items-center space-x-6">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                                    <span class="text-purple-600 dark:text-purple-400 font-bold">3</span>
                                </div>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Fake Captcha Interface</h3>
                                <p class="text-gray-600 dark:text-gray-400">Realistic reCAPTCHA simulation with auth code input</p>
                                <div class="mt-2 text-sm text-purple-600 dark:text-purple-400">
                                    Route: <code>/captcha</code>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5-6v6a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2z"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Arrow -->
                        <div class="flex justify-center">
                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                            </svg>
                        </div>

                        <!-- Step 4: Flow Detection -->
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center">Dual-Purpose Auth Code Logic</h3>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Unregistered Device -->
                                <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                                    <div class="flex items-center mb-3">
                                        <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-green-600 dark:text-green-400 font-bold text-sm">A</span>
                                        </div>
                                        <h4 class="font-semibold text-green-800 dark:text-green-200">Unregistered Device</h4>
                                    </div>
                                    <p class="text-sm text-green-700 dark:text-green-300 mb-3">
                                        Server detects nickname == null
                                    </p>
                                    <div class="space-y-2 text-sm">
                                        <div class="flex items-center text-green-600 dark:text-green-400">
                                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            Auth code = Friend code
                                        </div>
                                        <div class="flex items-center text-green-600 dark:text-green-400">
                                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            Navigate to signup
                                        </div>
                                        <div class="flex items-center text-green-600 dark:text-green-400">
                                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            Consume friend code
                                        </div>
                                    </div>
                                </div>

                                <!-- Registered Device -->
                                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                                    <div class="flex items-center mb-3">
                                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-blue-600 dark:text-blue-400 font-bold text-sm">B</span>
                                        </div>
                                        <h4 class="font-semibold text-blue-800 dark:text-blue-200">Registered Device</h4>
                                    </div>
                                    <p class="text-sm text-blue-700 dark:text-blue-300 mb-3">
                                        Server detects existing nickname
                                    </p>
                                    <div class="space-y-2 text-sm">
                                        <div class="flex items-center text-blue-600 dark:text-blue-400">
                                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            Auth code = Passkey
                                        </div>
                                        <div class="flex items-center text-blue-600 dark:text-blue-400">
                                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            Generate JWT token
                                        </div>
                                        <div class="flex items-center text-blue-600 dark:text-blue-400">
                                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            Navigate to chat
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (currentComponent == "news")
        {
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <svg class="h-5 w-5 text-blue-600 dark:text-blue-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div class="text-sm text-blue-800 dark:text-blue-200">
                            <p class="font-medium">Demo Mode - News Interface with Stealth Mode</p>
                            <p>Try entering "***" in the search field to activate stealth mode. The interface maintains the appearance of a regular news site.</p>
                        </div>
                    </div>
                </div>
            </div>
            <Platform.Razor.Features.Home.Listing.NewsListing />
        }
        else if (currentComponent == "captcha")
        {
            <div class="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
                <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <svg class="h-5 w-5 text-orange-600 dark:text-orange-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div class="text-sm text-orange-800 dark:text-orange-200">
                            <p class="font-medium">Demo Mode - Fake Captcha Interface</p>
                            <p>This realistic captcha simulation includes the auth code input that serves dual purposes based on device registration.</p>
                        </div>
                    </div>
                </div>
            </div>
            <FakeCaptchaScreen />
        }
        else if (currentComponent == "signup")
        {
            <div class="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
                <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <svg class="h-5 w-5 text-green-600 dark:text-green-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div class="text-sm text-green-800 dark:text-green-200">
                            <p class="font-medium">Demo Mode - Signup Form with Stealth Mode</p>
                            <p>Simplified signup form aligned with XAML architecture. Shows friend code integration when coming from stealth mode.</p>
                        </div>
                    </div>
                </div>
            </div>
            <Platform.Razor.Features.Authentication.SignUp.SignUpForm FromStealth="true" />
        }
        else if (currentComponent == "architecture")
        {
            <!-- System Architecture -->
            <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">Consolidated System Architecture</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Component Overview -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Core Components</h3>
                            <ul class="space-y-3 text-sm text-gray-600 dark:text-gray-300">
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    NewsListing - Integrated stealth mode
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    SignUpForm - XAML architecture aligned
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    FakeCaptchaScreen - Dual-purpose auth
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    LocalStorageService - Secure storage
                                </li>
                            </ul>
                        </div>

                        <!-- Architecture Benefits -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Consolidation Benefits</h3>
                            <ul class="space-y-3 text-sm text-gray-600 dark:text-gray-300">
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    Eliminated duplicate components
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    XAML architecture compliance
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    Simplified maintenance
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    Consistent patterns
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

@code {
    private string currentComponent = "flow";

    private void ShowComponent(string component)
    {
        currentComponent = component;
    }
}