﻿using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.Cient.Common.Data;
using System.Security.Claims;
using DeepMessage.Client.Common.Data;
using Platform.Framework.Core;
using Platform.Client.Services.Services;
using System.Security.Cryptography;
using System.Text;

namespace Platform.Client.Services.Features.Conversation;
public class ChatMessagesClientSideListingDataService : ClientSideListingDataService<ChatMessagesListingBusinessObject, ChatMessagesFilterBusinessObject>, IChatMessagesListingDataService
{
    private readonly AppDbContext context;
    private readonly ILocalStorageService localStorageService; 

    public ChatMessagesClientSideListingDataService ( AppDbContext context, ILocalStorageService localStorageService)
	{
        this.context = context;
        this.localStorageService = localStorageService; 
    } 
    public override IQueryable<ChatMessagesListingBusinessObject> GetQuery(ChatMessagesFilterBusinessObject filterBusinessObject)
    {
        var userId = Task.Run(() => (localStorageService.GetValue(ClaimTypes.NameIdentifier))).Result;

        // Get all messages for the conversation
        var query = from m in context.Messages
                    where m.ConversationId == filterBusinessObject.ConversationId
                    select new ChatMessagesListingBusinessObject
                    {
                        Id = m.Id,
                        Content = m.PlainContent,
                        IsIncoming = m.SenderId != userId,
                        Timestamp = m.CreatedAt,
                        DeliveryStatus = m.DeliveryStatus,
                    };
        // Apply search filter if SearchKey is provided
        if (!string.IsNullOrWhiteSpace(filterBusinessObject.SearchKey))
        {
            var searchTerm = filterBusinessObject.SearchKey.ToLower();
            query = query.Where(message =>
                message.Content != null && message.Content.ToLower().Contains(searchTerm)
            );
        }

        // Order by timestamp (oldest first for chat messages)
        return query.OrderBy(message => message.Timestamp);
    }

  
}
