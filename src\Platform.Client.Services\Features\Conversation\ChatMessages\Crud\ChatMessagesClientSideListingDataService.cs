﻿using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.Cient.Common.Data;
using System.Security.Claims;
using DeepMessage.Client.Common.Data;
using Platform.Framework.Core;
using Platform.Client.Services.Services;
using System.Security.Cryptography;
using System.Text;

namespace Platform.Client.Services.Features.Conversation;
public class ChatMessagesClientSideListingDataService : ClientSideListingDataService<ChatMessagesListingBusinessObject, ChatMessagesFilterBusinessObject>, IChatMessagesListingDataService
{
    private readonly AppDbContext context;
    private readonly ILocalStorageService localStorageService;
    private readonly IClientEncryptionService encryptionService;
    private readonly ISecureKeyManager secureKeyManager;

    public ChatMessagesClientSideListingDataService(AppDbContext context, ILocalStorageService localStorageService,
        IClientEncryptionService encryptionService, ISecureKeyManager secureKeyManager)
    {
        this.context = context;
        this.localStorageService = localStorageService;
        this.encryptionService = encryptionService;
        this.secureKeyManager = secureKeyManager;
    }
    public override IQueryable<ChatMessagesListingBusinessObject> GetQuery(ChatMessagesFilterBusinessObject filterBusinessObject)
    {
        var userId = Task.Run(() => (localStorageService.GetValue(ClaimTypes.NameIdentifier))).Result;

        // ✅ FIXED: Query MessageRecipient table for E2E encrypted messages
        var query = from mr in context.MessageRecipients
                    join m in context.Messages on mr.MessageId equals m.Id
                    where m.ConversationId == filterBusinessObject.ConversationId
                          && mr.RecipientId == userId // Only get messages for current user
                    select new ChatMessagesListingBusinessObject
                    {
                        Id = m.Id,
                        Content = mr.EncryptedContent, // ✅ Return encrypted content - UI layer must decrypt
                        IsIncoming = m.SenderId != userId,
                        Timestamp = m.CreatedAt,
                        DeliveryStatus = mr.DeliveryStatus // Use recipient-specific delivery status
                    };

        // Note: Search functionality disabled for encrypted content
        // Search would need to be implemented in UI layer after decryption
        if (!string.IsNullOrWhiteSpace(filterBusinessObject.SearchKey))
        {
            // For E2E encryption, search must be performed in UI layer after decryption
            // Server/service layer cannot search encrypted content
            // This could be enhanced by implementing client-side search after decryption
        }

        // Order by timestamp (oldest first for chat messages)
        return query.OrderBy(message => message.Timestamp);
    }

    /// <summary>
    /// Decrypts a message content using the user's private key
    /// This method should be called by the UI layer after retrieving encrypted messages
    /// </summary>
    /// <param name="encryptedContent">Base64-encoded encrypted message content</param>
    /// <returns>Decrypted plaintext message, or error message if decryption fails</returns>
    public string DecryptMessageContent(string encryptedContent)
    {
        try
        {
            if (string.IsNullOrEmpty(encryptedContent))
                return "[Empty Message]";

            if (!secureKeyManager.IsRSAKeyAvailable())
                return "[Decryption Key Not Available - Please Sign In]";

            using var privateKey = secureKeyManager.GetRSAPrivateKeyAsync();
            if (privateKey == null)
                return "[Private Key Not Available]";

            return encryptionService.DecryptWithRSAPrivateKey(encryptedContent, privateKey);
        }
        catch (Exception ex)
        {
            // Log the error but don't expose sensitive details
            return $"[Decryption Failed: {ex.Message}]";
        }
    }
}
