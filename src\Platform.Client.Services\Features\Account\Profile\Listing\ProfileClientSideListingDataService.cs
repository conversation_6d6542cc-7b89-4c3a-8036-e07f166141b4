﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Account;
using System.Security.Claims;
using Platform.Framework.Core;
namespace ModelFury.Briefly.MobileApp.Features.Account;
public class ProfileClientSideListingDataService : IProfileListingDataService
{

    private readonly BaseHttpClient _httpClient;
    private readonly ILocalStorageService localStorageService;

    public ProfileClientSideListingDataService(BaseHttpClient context, ILocalStorageService localStorageService)
    {
        _httpClient = context;
        this.localStorageService = localStorageService;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<PagedDataList<ProfileListingBusinessObject>> GetPaginatedItems(ProfileFilterBusinessObject filterBusinessObject)
    {
        //return await _httpClient.GetFromJsonAsync<PagedDataList<ProfileListingBusinessObject>>($"api/ProfileListing/GetPaginatedItems" + filterBusinessObject.ToQueryString());

        return new PagedDataList<ProfileListingBusinessObject>()
        {
            Items = new List<ProfileListingBusinessObject>()
             {
                 new ProfileListingBusinessObject()
                 {
                      Id =await localStorageService.GetValue( ClaimTypes.NameIdentifier),
                      NickName = await localStorageService.GetValue( ClaimTypes.Name)
                 }
             },
            TotalPages = 1,
            TotalRows = 1,
        };
    }
}
