﻿using ModelFury.Briefly.MobileApp.Features.Account;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Account;
namespace ModelFury.Briefly.MobileApp.Features.Account;
public class ProfileFormViewBase : FormBaseMaui<ProfileFormBusinessObject, ProfileFormViewModel, string, IProfileFormDataService>
{
    public ProfileFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }
}

public partial class ProfileFormView : ProfileFormViewBase
{
    public ProfileFormView(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
        InitializeComponent();
        BindingContext = this;
    }
}
