﻿using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Account;
using Platform.Client.Common.Features.AuthCodes;
namespace ModelFury.Briefly.MobileApp.Features.Account;
public class ProfileListingViewBase : ListingBaseMaui<ProfileListingViewModel, ProfileListingBusinessObject,
                                        ProfileFilterViewModel, ProfileFilterBusinessObject, IProfileListingDataService>
{
    public ProfileListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
    }
}


public partial class ProfileListingView : ProfileListingViewBase
{
    public ProfileListingViewModel _item = new ProfileListingViewModel();
    public ProfileListingViewModel Item
    {
        get
        {
            return _item;
        }
        set
        {
            _item = value;
            OnPropertyChanged();
        }
    }
    public ProfileListingView(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
        InitializeComponent();
        BindingContext = this;
    }

    protected override Task ItemsLoaded(IProfileListingDataService service)
    {
        Item.Id = Items.First().Id;
        Item.NickName = Items.First().NickName;
        return base.ItemsLoaded(service);
    }

    private async void Button_Clicked(object sender, EventArgs e)
    {
        await Shell.Current.GoToAsync("//signin");
        await Navigation.PopToRootAsync();
    }

    private void Button_Clicked_1(object sender, EventArgs e)
    {
        Navigation.PushAsync(new AuthCodeListingView(ScopeFactory));
    }
}


