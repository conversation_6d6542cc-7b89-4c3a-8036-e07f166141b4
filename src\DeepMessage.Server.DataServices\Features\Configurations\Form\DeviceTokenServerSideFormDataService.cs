﻿using DeepMessage.Framework.Core;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Configurations;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
namespace DeepMessage.Server.DataServices.Features.Configurations;
public class DeviceTokenServerSideFormDataService : IDeviceTokenFormDataService
{

    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public DeviceTokenServerSideFormDataService(AppDbContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(DeviceTokenFormBusinessObject formBusinessObject)
    {
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var device = await _context.UserDevices.FirstOrDefaultAsync(x => x.Id == formBusinessObject.Id);
        if (device == null)
        {
            device = new UserDevice
            {
                Id = formBusinessObject.Id,
                UserId = userId,
                Name = formBusinessObject.DeviceName,
                LastLogin = DateTime.UtcNow,
                Platform = formBusinessObject.Platform,
            };
            _context.UserDevices.Add(device);
        }

        device.DeviceToken = formBusinessObject.DeviceToken;
        await _context.SaveChangesAsync();
        return device.Id!;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<DeviceTokenFormBusinessObject?> GetItemByIdAsync(string id)
    {
        throw new NotImplementedException();
    }
}
