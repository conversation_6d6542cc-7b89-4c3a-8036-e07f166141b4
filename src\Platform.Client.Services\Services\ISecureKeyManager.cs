using Microsoft.Extensions.Logging;
using Platform.Framework.Core;
using System.Security.Cryptography;

namespace Platform.Client.Services.Services
{
    /// <summary>
    /// Secure in-memory key management service that handles cryptographic keys
    /// without persisting them to storage
    /// </summary>
    public interface ISecureKeyManager
    {
        /// <summary>
        /// Derives and stores AES key in memory from password and username-based salt
        /// </summary>
        Task<bool> DeriveAndStoreKeysAsync(string username, string password);

        /// <summary>
        /// Gets the decrypted RSA private key from memory
        /// </summary>
        Task<RSA?> GetRSAPrivateKeyAsync();

        /// <summary>
        /// Checks if RSA private key is available in memory
        /// </summary>
        bool IsRSAKeyAvailable();

        /// <summary>
        /// Clears all keys from memory (on app shutdown or logout)
        /// </summary>
        void ClearKeys();

        /// <summary>
        /// Derives AES key temporarily for specific operations (without storing)
        /// </summary>
        Task<byte[]> DeriveTemporaryAESKeyAsync(string username, string password);

        /// <summary>
        /// Checks if user needs to re-authenticate (cold boot scenario)
        /// </summary>
        bool RequiresAuthentication();

        /// <summary>
        /// Gets the current authenticated username
        /// </summary>
        string? GetAuthenticatedUsername();
    }

    /// <summary>
    /// Secure in-memory key manager implementation
    /// </summary>
    public class SecureKeyManager : ISecureKeyManager, IDisposable
    {
        private RSA? _rsaPrivateKey;
        private string? _authenticatedUsername;
        private bool _isAuthenticated;
        private readonly object _lockObject = new object();
        private bool _disposed = false;
        private readonly ILocalStorageService _localStorageService;
        private readonly ILogger<SecureKeyManager> logger;
        private const int PBKDF2_ITERATIONS = 100000;
        private const int SALT_SIZE = 32;
        private const int AES_KEY_SIZE = 32; // 256 bits

        public SecureKeyManager(ILocalStorageService localStorageService, ILogger<SecureKeyManager> logger)
        {
            _localStorageService = localStorageService;
            this.logger = logger;
        }

        public async Task<bool> DeriveAndStoreKeysAsync(string username, string password)
        {
            try
            {
                lock (_lockObject)
                {
                    if (_disposed) return false;
                    
                    // Clear any existing keys
                    ClearKeys();
                }

                // Derive AES key using username as salt (improved security)
                var salt = GenerateDeterministicSalt(username);
                var aesKey = await DeriveAESKeyFromPasswordAsync(password, salt);

                // Load and decrypt RSA private key from local storage
                var encryptedPrivateKey = await GetEncryptedPrivateKeyFromStorageAsync(username);
                if (string.IsNullOrEmpty(encryptedPrivateKey))
                {
                    return false;
                }

                var privateKeyPem = await DecryptRSAPrivateKeyAsync(encryptedPrivateKey, aesKey);
                
                lock (_lockObject)
                {
                    if (_disposed) return false;

                    // Store RSA key in memory
                    _rsaPrivateKey = RSA.Create();
                    var privateKeyBytes = Convert.FromBase64String(privateKeyPem);
                    _rsaPrivateKey.ImportRSAPrivateKey(privateKeyBytes, out _);
                    
                    _authenticatedUsername = username;
                    _isAuthenticated = true;
                }

                // Clear sensitive data from memory
                Array.Clear(aesKey, 0, aesKey.Length);
                
                return true;
            }
            catch(Exception ex)
            {
                lock (_lockObject)
                {
                    ClearKeys();
                }
                logger.LogError(ex, "Error deriving and storing keys");     
                return false;
            }
        }

        public async Task<RSA?> GetRSAPrivateKeyAsync()
        {
            await Task.CompletedTask;
            lock (_lockObject)
            {
                if (_disposed || !_isAuthenticated || _rsaPrivateKey == null)
                    return null;

                // Return a copy to prevent external modification
                var copy = RSA.Create();
                copy.ImportParameters(_rsaPrivateKey.ExportParameters(true));
                return copy;
            }
        }

        public bool IsRSAKeyAvailable()
        {
            lock (_lockObject)
            {
                return !_disposed && _isAuthenticated && _rsaPrivateKey != null;
            }
        }

        public void ClearKeys()
        {
            lock (_lockObject)
            {
                _rsaPrivateKey?.Dispose();
                _rsaPrivateKey = null;
                _authenticatedUsername = null;
                _isAuthenticated = false;
            }
        }

        public async Task<byte[]> DeriveTemporaryAESKeyAsync(string username, string password)
        {
            var salt = GenerateDeterministicSalt(username);
            return await DeriveAESKeyFromPasswordAsync(password, salt);
        }

        public bool RequiresAuthentication()
        {
            lock (_lockObject)
            {
                return _disposed || !_isAuthenticated || _rsaPrivateKey == null;
            }
        }

        public string? GetAuthenticatedUsername()
        {
            lock (_lockObject)
            {
                return _isAuthenticated ? _authenticatedUsername : null;
            }
        }

        /// <summary>
        /// Generates a deterministic salt from username for consistent key derivation
        /// </summary>
        private byte[] GenerateDeterministicSalt(string username)
        {
            // Use SHA-256 to create a deterministic salt from username
            // This ensures the same salt is always generated for the same username
            using var sha256 = SHA256.Create();
            var usernameBytes = System.Text.Encoding.UTF8.GetBytes(username.ToLowerInvariant());
            var hash = sha256.ComputeHash(usernameBytes);
            
            // Use the first 32 bytes of the hash as salt
            var salt = new byte[SALT_SIZE];
            Array.Copy(hash, salt, Math.Min(hash.Length, SALT_SIZE));
            
            return salt;
        }

        /// <summary>
        /// Derives AES key using PBKDF2
        /// </summary>
        private async Task<byte[]> DeriveAESKeyFromPasswordAsync(string password, byte[] salt)
        {
            return await Task.Run(() =>
            {
                using var pbkdf2 = new Rfc2898DeriveBytes(
                    System.Text.Encoding.UTF8.GetBytes(password),
                    salt,
                    PBKDF2_ITERATIONS,
                    HashAlgorithmName.SHA256);

                return pbkdf2.GetBytes(AES_KEY_SIZE);
            });
        }

        /// <summary>
        /// Decrypts RSA private key using AES
        /// </summary>
        private async Task<string> DecryptRSAPrivateKeyAsync(string encryptedPrivateKey, byte[] aesKey)
        {
            return await Task.Run(() =>
            {
                var data = Convert.FromBase64String(encryptedPrivateKey);
                
                // Extract IV and ciphertext
                var iv = new byte[16]; // AES block size
                var ciphertextBytes = new byte[data.Length - 16];
                Array.Copy(data, 0, iv, 0, 16);
                Array.Copy(data, 16, ciphertextBytes, 0, ciphertextBytes.Length);

                using var aes = Aes.Create();
                aes.Key = aesKey;
                aes.IV = iv;

                using var decryptor = aes.CreateDecryptor();
                var plaintextBytes = decryptor.TransformFinalBlock(ciphertextBytes, 0, ciphertextBytes.Length);

                return System.Text.Encoding.UTF8.GetString(plaintextBytes);
            });
        }

        /// <summary>
        /// Retrieves encrypted private key from local storage
        /// </summary>
        private async Task<string?> GetEncryptedPrivateKeyFromStorageAsync(string username)
        {
            try
            {
                return await _localStorageService.GetValue("pub2e_");
            }
            catch
            {
                return null;
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                ClearKeys();
                _disposed = true;
            }
        }
    }
}