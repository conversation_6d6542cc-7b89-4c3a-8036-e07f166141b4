﻿@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.Home
@using Platform.Client.Services.Features.Home
@using Platform.Razor.Components
@page "/"
@inherits ListingBase<NewsListingViewModel,NewsListingBusinessObject,
        NewsFilterViewModel,NewsFilterBusinessObject, INewsListingDataService>

<!-- Main Container with Nothing Phone black background and proper scroll -->
<div class="h-screen bg-gradient-nothing-subtle dark:bg-nothing-black-950 flex flex-col page-fade-in">
    <!-- Header Section with Search - Nothing Phone Black Theme -->
    <div class="bg-gradient-nothing dark:bg-gradient-nothing-dark shadow-lg border-b border-nothing-black-200 dark:border-nothing-black-700 flex-shrink-0">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <!-- Title with Nothing Phone styling -->
            <div class="mb-6">
                <h1 class="text-3xl font-bold text-white dark:text-white">Latest News</h1>
                <p class="mt-2 text-sm text-white/80 dark:text-white/70">Stay updated with the latest news and stories</p>
            </div>

            <!-- Search Bar with Stealth Mode Integration -->
            <div class="relative max-w-md">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <input type="text"
                       @bind="FilterViewModel.SearchKey"
                       id="newsSearchInput"
                       placeholder="Search news articles..."
                       class="block w-full pl-10 pr-3 py-2 border border-white/20 dark:border-white/30 rounded-lg leading-5 bg-white/10 dark:bg-white/5 backdrop-blur-sm text-white dark:text-white placeholder-white/60 dark:placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 sm:text-sm transition-all duration-200 min-h-[44px]" />
                @if (!string.IsNullOrEmpty(FilterViewModel.SearchKey))
                {
                    <button @onclick="ClearSearch"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <svg class="h-5 w-5 text-white/60 hover:text-white transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                }
            </div>
        </div>
    </div>

    <!-- Scrollable Content Section -->
    <div class="flex-1 overflow-y-auto">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        @if (IsWorking)
        {
            <!-- Loading State -->
            <div class="flex items-center justify-center py-12">
                <div class="flex items-center space-x-3">
                    <div class="spinner-nothing h-8 w-8"></div>
                    <span class="text-lg text-nothing-black-700 dark:text-nothing-black-300">Loading news articles...</span>
                </div>
            </div>
        }
        else if (!string.IsNullOrEmpty(Error))
        {
            <!-- Error State -->
            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
                <div class="flex items-center">
                    <svg class="h-6 w-6 text-red-600 dark:text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error loading news</h3>
                        <p class="mt-1 text-sm text-red-700 dark:text-red-300">@Error</p>
                    </div>
                </div>
                <button @onclick="RefreshItems"
                        class="mt-4 bg-nothing-red-500 hover:bg-nothing-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl">
                    Try Again
                </button>
            </div>
        }
        else if (Items?.Count == 0)
        {
            <!-- Empty State -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                </svg>
                <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">No news articles found</h3>
                <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    @if (!string.IsNullOrEmpty(FilterViewModel.SearchKey))
                    {
                        <span>No articles match your search for "@FilterViewModel.SearchKey"</span>
                    }
                    else
                    {
                        <span>There are no news articles available at the moment.</span>
                    }
                </p>
                @if (!string.IsNullOrEmpty(FilterViewModel.SearchKey))
                {
                    <button @onclick="ClearSearch"
                            class="mt-4 bg-nothing-black-800 hover:bg-nothing-black-900 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl">
                        Clear Search
                    </button>
                }
            </div>
        }
        else
        {
            <!-- Search Results Info -->
            @if (!string.IsNullOrEmpty(FilterViewModel.SearchKey))
            {
                <div class="mb-6 p-4 bg-nothing-black-50 dark:bg-nothing-black-900/20 border border-nothing-black-200 dark:border-nothing-black-800 rounded-lg">
                    <p class="text-sm text-nothing-black-800 dark:text-nothing-black-200">
                        <span class="font-medium">@TotalRecords</span> articles found for
                        <span class="font-medium">"@FilterViewModel.SearchKey"</span>
                        <button @onclick="ClearSearch" class="ml-2 text-nothing-black-600 dark:text-nothing-black-400 hover:text-nothing-black-800 dark:hover:text-nothing-black-200 underline">
                            Clear search
                        </button>
                    </p>
                </div>
            }

            <!-- News Articles Grid -->
            <div class="grid gap-6 md:gap-8">
                @foreach (var item in Items)
                {
                    <article class="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden group cursor-pointer"
                             @onclick="() => OpenArticle(item.Link)">
                        <div class="p-6">
                            <!-- Article Header -->
                            <div class="flex items-start justify-between mb-4">
                                <time class="text-sm text-gray-500 dark:text-gray-400 font-medium">
                                    @item.PubDate.ToString("MMM dd, yyyy • h:mm tt")
                                </time>
                                <svg class="h-5 w-5 text-gray-400 group-hover:text-nothing-red-500 dark:group-hover:text-nothing-red-400 transition-colors duration-200"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                            </div>

                            <!-- Article Title -->
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-nothing-red-500 dark:group-hover:text-nothing-red-400 transition-colors duration-200 line-clamp-2">
                                @item.Title
                            </h2>

                            <!-- Article Description -->
                            @if (!string.IsNullOrEmpty(item.Description))
                            {
                                <p class="text-gray-600 dark:text-gray-300 leading-relaxed line-clamp-3">
                                    @GetCleanDescription(item.Description)
                                </p>
                            }

                            <!-- Read More Link -->
                            <div @onclick="() => OpenArticle(item.Link)" class="mt-4 flex items-center text-nothing-red-500 dark:text-nothing-red-400 font-medium text-sm group-hover:text-nothing-red-600 dark:group-hover:text-nothing-red-300 transition-colors duration-200">
                                <span>Read full article</span>
                                <svg class="ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                        </div>
                    </article>
                }
            </div>

            <!-- Pagination -->
            @if (UsePagination && TotalRecords > PageSize)
            {
               @*  <div class="mt-12 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 pt-8">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <!-- Mobile Pagination -->
                        <button @onclick="PreviousPage"
                                disabled="@(CurrentPage <= 1)"
                                class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed">
                            Previous
                        </button>
                        <button @onclick="NextPage"
                                disabled="@(CurrentPage >= Math.Ceiling((double)TotalRecords / PageSize))"
                                class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed">
                            Next
                        </button>
                    </div>

                    <!-- Desktop Pagination -->
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700 dark:text-gray-300">
                                Showing <span class="font-medium">@((CurrentPage - 1) * PageSize + 1)</span> to
                                <span class="font-medium">@Math.Min(CurrentPage * PageSize, TotalRecords)</span> of
                                <span class="font-medium">@TotalRecords</span> articles
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <!-- Previous Button -->
                                <button @onclick="PreviousPage"
                                        disabled="@(CurrentPage <= 1)"
                                        class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </button>

                                <!-- Page Numbers -->
                                @for (int i = Math.Max(1, CurrentPage - 2); i <= Math.Min(Math.Ceiling((double)TotalRecords / PageSize), CurrentPage + 2); i++)
                                {
                                    var pageNumber = i;
                                    <button @onclick="() => GoToPage(pageNumber)"
                                            class="@(pageNumber == CurrentPage ?
                                                "bg-blue-50 dark:bg-blue-900/50 border-blue-500 dark:border-blue-400 text-blue-600 dark:text-blue-400" :
                                                "bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700")
                                            relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        @pageNumber
                                    </button>
                                }

                                <!-- Next Button -->
                                <button @onclick="NextPage"
                                        disabled="@(CurrentPage >= Math.Ceiling((double)TotalRecords / PageSize))"
                                        class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </nav>
                        </div>
                    </div>
                </div> *@
            }
        }
        </div>
    </div>
</div>
