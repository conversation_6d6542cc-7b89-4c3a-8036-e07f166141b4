﻿using DeepMessage.Framework.Core;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
namespace ModelFury.Briefly.MobileApp.Features.Account;
public class SignupFormViewModel : ObservableBase, IValidateable
{ 

    // Keep NickName for backward compatibility
    [Required]
    public string? NickName { get; set; }

    [Required(ErrorMessage = "PassKey is required")]
    [MinLength(8, ErrorMessage = "PassKey must be at least 8 characters long")]
    private string? _passKey;

    public string? PassKey
    {
        get { return _passKey; }
        set
        {
            SetField(ref _passKey, value);
            NotifyPropertyChanged(nameof(PasswordStrengthText));
            NotifyPropertyChanged(nameof(PasswordStrengthColor));
        }
    }
     
    public string Pub1 { get; set; } = null!;

    public string Pub2 { get; set; } = null!;
     

    public string DeviceString { get; set; } = null!;

    [Required]
    public string ReferralCode { get; set; } = null!;


    private bool _showPassword;
    public bool ShowPassword
    {
        get { return _showPassword; }
        set { SetField(ref _showPassword, value); }
    }

    private bool _showConfirmPassword;
    public bool ShowConfirmPassword
    {
        get { return _showConfirmPassword; }
        set { SetField(ref _showConfirmPassword, value); }
    }

    public string? ProfilePictureUrl { get; set; }

    public int PasswordStrength
    {
        get
        {
            if (string.IsNullOrEmpty(PassKey))
                return 0;

            int score = 0;

            // Length check
            if (PassKey.Length >= 8) score++;
            if (PassKey.Length >= 12) score++;

            // Character variety checks
            if (Regex.IsMatch(PassKey, @"[a-z]")) score++; // lowercase
            if (Regex.IsMatch(PassKey, @"[A-Z]")) score++; // uppercase
            if (Regex.IsMatch(PassKey, @"[0-9]")) score++; // numbers
            if (Regex.IsMatch(PassKey, @"[^a-zA-Z0-9]")) score++; // special characters

            return Math.Min(score, 4); // Cap at 4 for UI purposes
        }
    }

    public string PasswordStrengthText
    {
        get
        {
            return PasswordStrength switch
            {
                0 => "",
                1 => "Weak",
                2 => "Fair",
                3 => "Good",
                4 => "Strong",
                _ => ""
            };
        }
    }

    public string PasswordStrengthColor
    {
        get
        {
            return PasswordStrength switch
            {
                0 => "transparent",
                1 => "#ef4444", // red
                2 => "#f59e0b", // amber
                3 => "#10b981", // emerald
                4 => "#059669", // emerald-600
                _ => "transparent"
            };
        }
    }

    public void Validate()
    {
        
        if (string.IsNullOrEmpty(PassKey))
        {
            throw new ValidationException("Password is required");
        }

        if (PassKey.Length < 8)
        {
            throw new ValidationException("Password must be at least 8 characters long");
        }
 

        if (PasswordStrength < 2)
        {
            throw new ValidationException("Password is too weak. Please use a stronger password");
        }
    }
}
