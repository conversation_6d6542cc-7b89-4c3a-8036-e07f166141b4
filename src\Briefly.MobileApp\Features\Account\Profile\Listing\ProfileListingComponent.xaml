﻿<?xml version="1.0" encoding="utf-8" ?>
<local:ProfileListingViewBase
    x:Class="ModelFury.Briefly.MobileApp.Features.Account.ProfileListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:ModelFury.Briefly.MobileApp.Features.Account"
    Title="ProfileListingView"
    x:DataType="local:ProfileListingView"
    BackgroundColor="Black"
    Shell.TitleColor="White">
    <Border BackgroundColor="{AppThemeBinding Light=WhiteSmoke, Dark=Black}" Stroke="{AppThemeBinding Light=Gainsboro, Dark={StaticResource Gray800}}">
        <Border.StrokeShape>
            <RoundRectangle CornerRadius="16,16,0,0" />
        </Border.StrokeShape>
        <VerticalStackLayout>
            <Label
                Margin="8"
                HorizontalOptions="Center"
                Text="{Binding Item.Id}"
                VerticalOptions="Center" />

            <Label
                Margin="8"
                HorizontalOptions="Center"
                Text="{Binding Item.NickName}"
                VerticalOptions="Center" />

            <Button
                Margin="8"
                BackgroundColor="{AppThemeBinding Light={StaticResource Gray100},
                                                  Dark={StaticResource Gray700}}"
                Clicked="Button_Clicked_1"
                Text="Friend Codes"
                TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                            Dark={StaticResource Gray200}}" />

            <Button
                Margin="8"
                BackgroundColor="{AppThemeBinding Light={StaticResource Gray100},
                                                  Dark={StaticResource Gray700}}"
                Clicked="Button_Clicked"
                Text="Logout"
                TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                            Dark={StaticResource Gray200}}" />
        </VerticalStackLayout>
    </Border>
</local:ProfileListingViewBase>
