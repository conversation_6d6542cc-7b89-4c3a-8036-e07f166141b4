﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Features.Home;
using System.Threading.Tasks;
using System.Xml.Linq;
namespace DeepMessage.Server.DataServices.Features.Home;
public class NewsServerSideListingDataService : ServerSideListingDataService<NewsListingBusinessObject, NewsFilterBusinessObject>, INewsListingDataService
{

	//private readonly AppDbContext _context;

	//public NewsServerSideListingDataService (AppDbContext context)
	//{
		//_context = context;
	//}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public override IQueryable<NewsListingBusinessObject> GetQuery(NewsFilterBusinessObject filterBusinessObject)
	{
        var newsItems = RssService.GetNewsAsync().GetAwaiter().GetResult().AsQueryable();

        // Apply search filter if SearchKey is provided
        if (!string.IsNullOrWhiteSpace(filterBusinessObject.SearchKey))
        {
            var searchTerm = filterBusinessObject.SearchKey.ToLower();
            newsItems = newsItems.Where(item =>
                (item.Title != null && item.Title.ToLower().Contains(searchTerm)) ||
                (item.Description != null && item.Description.ToLower().Contains(searchTerm))
            );
        }

        // Order by publication date (newest first)
        return newsItems.OrderByDescending(item => item.PubDate);
    }
    


    public class RssService
    {
        private const string FeedUrl = "https://feeds.bbci.co.uk/news/rss.xml";

        public static async Task<List<NewsListingBusinessObject>> GetNewsAsync()
        {
            try
            {
                var client = new HttpClient();
                var response = await client.GetStringAsync(FeedUrl);
                var doc = XDocument.Parse(response);

                var items = doc.Descendants("item").Select(item => new NewsListingBusinessObject
                {
                    Title = item.Element("title")?.Value,
                    Description = item.Element("description")?.Value,
                    Link = item.Element("link")?.Value,
                    PubDate = DateTime.TryParse(item.Element("pubDate")?.Value, out var date) ? date : DateTime.MinValue
                }).ToList();

                return items;
            }
            catch
            {
                return new List<NewsListingBusinessObject>();
            }
        }
    }

    public class NewsItemViewModel
    {
        public string? Title { get; set; } = null!;
        public string? Description { get; set; } = null!;
        public string? Link { get; set; } = null!;
        public DateTime PubDate { get; set; }
    }

}
