﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Conversation;
using System.Security.Claims;
using DeepMessage.Client.Common.Data;
using DeepMessage.MauiApp.Services;
using Microsoft.EntityFrameworkCore;
using Platform.Framework.Core;
using Platform.Client.Services.Services;
namespace Platform.Client.Services.Features.Conversation;
public class ChatMessageClientSideFormDataService : IChatMessageFormDataService
{

    private readonly BaseHttpClient _httpClient;
    private readonly AppDbContext context;
    private readonly ILocalStorageService localStorageService;
    private readonly ChatSyncUpService chatSyncUpService;
    private readonly IClientEncryptionService encryptionService;

    public ChatMessageClientSideFormDataService(BaseHttpClient httpClient, AppDbContext context,
        ILocalStorageService localStorageService, ChatSyncUpService chatThreadsSyncService,
        IClientEncryptionService encryptionService)
    {
        _httpClient = httpClient;
        this.context = context;
        this.localStorageService = localStorageService;
        this.chatSyncUpService = chatThreadsSyncService;
        this.encryptionService = encryptionService;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(ChatMessageFormBusinessObject formBusinessObject)
    {
        var userId = await localStorageService.GetValue(ClaimTypes.NameIdentifier);
        ArgumentException.ThrowIfNullOrEmpty(formBusinessObject.ConversationId);
        ArgumentException.ThrowIfNullOrEmpty(userId);
        ArgumentException.ThrowIfNullOrEmpty(formBusinessObject.Content);

        // Create message with NO plaintext content for E2E encryption
        var message = new Message()
        {
            Id = Guid.CreateVersion7().ToString(),
            ConversationId = formBusinessObject.ConversationId,
            CreatedAt = DateTime.UtcNow,
            PlainContent = null, // ✅ NO PLAINTEXT STORAGE - E2E ENCRYPTION
            DeliveryStatus = DeepMessage.ServiceContracts.Enums.DeliveryStatus.QueuedToUpSync,
            SenderId = userId
        };

        context.Messages.Add(message);
        await context.SaveChangesAsync();

        // Get conversation participants with their public keys
        var participantsWithKeys = await (from cp in context.ConversationParticipants
                                          from f in context.Friendships.Where(x=>x.FriendId == cp.UserId).DefaultIfEmpty()
                                          where cp.ConversationId == message.ConversationId
                                          select new
                                          {
                                              UserId = cp.UserId,
                                              PublicKey =  f.Pub1, // Friend's public key
                                              IsSender = cp.UserId == userId
                                          }).ToListAsync();

        // Get sender's own public key from local storage or friends table
        string? senderPublicKey = null;
        if (participantsWithKeys.Any(p => p.IsSender))
        {
            // For sender's copy, we need to get their own public key
            // This should be stored locally or retrieved from server during authentication
            // For now, we'll get it from the friendship table (reverse lookup)
            senderPublicKey = await localStorageService.GetValue("pub1o_");

            // If no friendship found, try to get from local storage or skip sender's copy
            // In a real implementation, sender's public key should be available locally
        }

        // Create encrypted MessageRecipient records for each participant
        foreach (var participant in participantsWithKeys)
        {
            string encryptedContent;

            if (participant.IsSender)
            {
                // For sender's copy, use their own public key if available
                if (string.IsNullOrEmpty(senderPublicKey))
                {
                    throw new InvalidOperationException($"Public key not found for participant {participant.UserId}");
                }
                encryptedContent = encryptionService.EncryptWithRSAPublicKey(formBusinessObject.Content, senderPublicKey);
            }
            else
            {
                // For recipient's copy, use their public key
                if (string.IsNullOrEmpty(participant.PublicKey))
                {
                    throw new InvalidOperationException($"Public key not found for participant {participant.UserId}");
                }
                encryptedContent = encryptionService.EncryptWithRSAPublicKey(formBusinessObject.Content, participant.PublicKey);
            }

            var messageRecipient = new MessageRecipient()
            {
                Id = Guid.CreateVersion7().ToString().ToLower(),
                MessageId = message.Id,
                RecipientId = participant.UserId,
                EncryptedContent = encryptedContent, // ✅ PROPERLY ENCRYPTED CONTENT
                IsRead = participant.IsSender, // Mark as read for sender
                SyncStatus = 0
            };

            context.MessageRecipients.Add(messageRecipient);
        }

        await context.SaveChangesAsync();
        chatSyncUpService.Sync(new ChatSyncItem() { Id = message.Id, SyncType = 1 });
        return message.Id;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<ChatMessageFormBusinessObject> GetItemByIdAsync(string id)
    {
        return await _httpClient.GetFromJsonAsync<ChatMessageFormBusinessObject>($"api/ChatMessageForm/GetItemById?id=" + id);

    }
}
