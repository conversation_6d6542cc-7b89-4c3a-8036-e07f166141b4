﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Conversation;
using System.Security.Claims;
using DeepMessage.Client.Common.Data;
using DeepMessage.MauiApp.Services;
using Microsoft.EntityFrameworkCore;
using Platform.Framework.Core;
namespace Platform.Client.Services.Features.Conversation;
public class ChatMessageClientSideFormDataService : IChatMessageFormDataService
{

    private readonly BaseHttpClient _httpClient;
    private readonly AppDbContext context;
    private readonly ILocalStorageService localStorageService;
    private readonly ChatSyncUpService chatSyncUpService;

    public ChatMessageClientSideFormDataService(BaseHttpClient httpClient, AppDbContext context,
        ILocalStorageService localStorageService, ChatSyncUpService chatThreadsSyncService)
    {
        _httpClient = httpClient;
        this.context = context;
        this.localStorageService = localStorageService;
        this.chatSyncUpService = chatThreadsSyncService;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(ChatMessageFormBusinessObject formBusinessObject)
    {
        //return await _httpClient.PostAsJsonAsync<string>($"api/ChatMessageForm/Save", formBusinessObject);

        var userId = Task.Run(() => (localStorageService.GetValue(ClaimTypes.NameIdentifier))).Result;
        ArgumentException.ThrowIfNullOrEmpty(formBusinessObject.ConversationId);
        ArgumentException.ThrowIfNullOrEmpty(userId);

        var message = new Message()
        {
            Id = Guid.CreateVersion7().ToString(),
            ConversationId = formBusinessObject.ConversationId,
            CreatedAt = DateTime.UtcNow,
            PlainContent = formBusinessObject.Content,
            DeliveryStatus =  DeepMessage.ServiceContracts.Enums.DeliveryStatus.QueuedToUpSync,
            SenderId = userId
        };

        context.Messages.Add(message);
        await context.SaveChangesAsync();

        //todo: cache
        var conversationParticipants = await context.ConversationParticipants.Where(x => x.ConversationId == message.ConversationId).ToListAsync();

        foreach (var participant in conversationParticipants)
        {
            var messageRecipient = await context.MessageRecipients.FirstOrDefaultAsync(x => x.MessageId == message.Id && x.RecipientId == participant.UserId);
            if (messageRecipient == null)
            {
                messageRecipient = new MessageRecipient()
                {
                    Id = Guid.CreateVersion7().ToString().ToLower(),
                    MessageId = message.Id,
                    //UserDeviceId = participant.UserId,
                    RecipientId = participant.UserId,
                    EncryptedContent = message.PlainContent ?? string.Empty,
                };
            }
            context.MessageRecipients.Add(messageRecipient);
            await context.SaveChangesAsync();

        }

        chatSyncUpService.Sync(new ChatSyncItem() { Id = message.Id, SyncType = 1 });
        return message.Id;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<ChatMessageFormBusinessObject> GetItemByIdAsync(string id)
    {
        return await _httpClient.GetFromJsonAsync<ChatMessageFormBusinessObject>($"api/ChatMessageForm/GetItemById?id=" + id);

    }
}
