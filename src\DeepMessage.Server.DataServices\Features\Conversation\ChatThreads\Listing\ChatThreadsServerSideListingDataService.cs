﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
namespace DeepMessage.Server.DataServices.Features.Conversation;
public class ChatThreadsServerSideListingDataService : ServerSideListingDataService<ChatThreadsListingBusinessObject, ChatThreadsFilterBusinessObject>, IChatThreadsListingDataService
{
    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public ChatThreadsServerSideListingDataService(AppDbContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public override IQueryable<ChatThreadsListingBusinessObject> GetQuery(ChatThreadsFilterBusinessObject filterBusinessObject)
    {
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        var conversations = (from c in _context.Conversations
                             from p in _context.ConversationParticipants.Where(x => x.ConversationId == c.Id)
                             where p.UserId == userId
                             select c.Id).ToList();

        var query = (from c in _context.Conversations
                     from p in _context.ConversationParticipants.Where(x => x.ConversationId == c.Id)
                     from f in _context.Friendships.Where(x => x.FriendId == p.UserId)
                     where conversations.Contains(c.Id) && p.UserId != userId
                     select new ChatThreadsListingBusinessObject
                     {
                         Id = c.Id,
                         Avatar = f.DisplayPictureUrl,
                         Name = f.Name,
                         LastMessage = _context.Messages.Where(x => x.ConversationId == c.Id)
                             .OrderByDescending(x => x.CreatedAt)
                             .Select(x => x.PlainContent)
                             .FirstOrDefault(),
                         LastMessageTime = _context.Messages.Where(x => x.ConversationId == c.Id)
                             .OrderByDescending(x => x.CreatedAt)
                             .Select(x => x.CreatedAt)
                             .FirstOrDefault()
                     });

        // Apply search filter if SearchKey is provided
        if (!string.IsNullOrWhiteSpace(filterBusinessObject.SearchKey))
        {
            var searchTerm = filterBusinessObject.SearchKey.ToLower();
            query = query.Where(thread =>
                thread.Name.ToLower().Contains(searchTerm) ||
                (thread.LastMessage != null && thread.LastMessage.ToLower().Contains(searchTerm))
            );
        }

        // Order by last message time (most recent first)
        return query.OrderByDescending(thread => thread.LastMessageTime);
    }

    protected override PagedDataList<ChatThreadsListingBusinessObject> OnItemsLoaded(PagedDataList<ChatThreadsListingBusinessObject> items)
    {
        ArgumentNullException.ThrowIfNull(items?.Items);

        items.Items = items.Items.DistinctBy(x => x.Id).ToList();
        return items;
    }
}
