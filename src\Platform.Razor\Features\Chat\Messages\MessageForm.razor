@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.Conversation
@using Platform.Client.Services.Features.Conversation
@using Platform.Framework.Core
@inherits FormBase<ChatMessageFormBusinessObject, ChatMessageFormViewModel, string, IChatMessageFormDataService>

<div class="flex items-end space-x-3">
    <!-- Message Input -->
    <div class="flex-1">
        <div class="relative">
            <textarea @ref="messageInput"
                      @bind="SelectedItem!.Content"
                      @bind:event="oninput" 
                      placeholder="Type a message..." 
                      id="_message"
                      class="block w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none"
                      style="min-height: 32px; max-height: 120px; overflow-y: auto;"></textarea>

            <button type="button" @onclick="TriggerFileUpload"
                    class="absolute right-3 bottom-3 p-1 text-gray-500 dark:text-gray-400 ">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                </svg>
            </button>

        </div>

        <!-- File Upload Area (Hidden) -->
        <InputFile onchange="HandleFileSelected" multiple accept="image/*,video/*,.pdf,.doc,.docx" style="display: none;" />
         
    </div>

    <!-- Action Buttons -->
    <div class="flex items-center space-x-2">
        <!-- Attachment Button -->
        <!-- Send Button -->
        <label for="_message" type="button" @onclick="SendMessage"
                disabled="@(IsWorking || string.IsNullOrWhiteSpace(SelectedItem?.Content))"
                class="p-3 h-10 w-10 bg-nothing-black-800 disabled:bg-gray-300 dark:disabled:bg-gray-600 text-nothing-red-600 rounded-full  disabled:cursor-not-allowed">
            <img src="images/paper_plane_top_solid.svg" class="h-5 w-5" />
        </label>
    </div>
</div>

<!-- File Preview Area -->
@if (selectedFiles?.Count > 0)
{
    <div class="mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Attachments (@selectedFiles.Count)</span>
            <button type="button" @onclick="ClearFiles"
                    class="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                Clear all
            </button>
        </div>
        <div class="space-y-2">
            @foreach (var file in selectedFiles)
            {
                <div class="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded border">
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="text-sm text-gray-700 dark:text-gray-300">@file.Name</span>
                        <span class="text-xs text-gray-500">(@FormatFileSize(file.Size))</span>
                    </div>
                    <button type="button" @onclick="() => RemoveFile(file)"
                            class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            }
        </div>
    </div>
}

<!-- Error Display -->
@if (!string.IsNullOrEmpty(Error))
{
    <div class="mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
        <div class="flex items-center">
            <svg class="h-5 w-5 text-red-600 dark:text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-sm text-red-800 dark:text-red-200">@Error</span>
        </div>
    </div>
}
