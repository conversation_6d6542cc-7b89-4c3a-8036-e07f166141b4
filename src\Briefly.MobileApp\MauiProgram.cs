﻿using CommunityToolkit.Maui;
using DeepMessage.Client.Common.Data;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Platform.Client.Common.Features.Conversation;
using Platform.Client.Common.Features.Friends;
using DeepMessage.MauiApp.Services;
using DeepMessage.MauiShared;
using MobileApp.MauiShared;
using DeepMessage.ServiceContracts.Features.Account;
using ModelFury.Briefly.MobileApp.Features.Account; 
using Microsoft.Maui.LifecycleEvents;
using Plugin.Firebase.CloudMessaging; 
using DeepMessage.ServiceContracts.Features.Configurations;
using DeepMessage.ServiceContracts.Features.AuthCodes;
using Platform.Client.Common.Features.AuthCodes;
using Plugin.Firebase.Crashlytics;
using Platform.Framework.Core;
using Platform.Client.Services;
using DeepMessage.Framework.Core;
using Platform.Client.Services.Features.Friends;
using Platform.Client.Services.Features.Conversation.ChatThreads.Form;
using Platform.Client.Services.Features.Conversation;
using Platform.Client.Services.Features.Configurations;
using Platform.Client.Services.Features.AuthCodes;










#if ANDROID
using Plugin.Firebase.Core.Platforms.Android;
#endif

namespace ModelFury.Briefly.MobileApp;

public static class MauiProgram
{

#if DEBUG
    public static string APIUrl = "https://*********:7073";
    //public static string APIUrl = "https://briefly.azurewebsites.net";
#else
    //public static string APIUrl = "https://********:7073";
    public static string APIUrl = "https://briefly.azurewebsites.net";
#endif

    public static string? ChatHubUrl;
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:Validate platform compatibility", Justification = "<Pending>")]
    public static Microsoft.Maui.Hosting.MauiApp CreateMauiApp()
    {
        ChatHubUrl = $"{APIUrl}/chathub";
        var builder = Microsoft.Maui.Hosting.MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .UseMauiCommunityToolkit()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                fonts.AddFont("Jost-Regular.ttf", "Jost");
            });
        builder.RegisterFirebaseServices();
        builder.Services.AddSingleton<ChatSyncUpService>();
        builder.Services.AddSingleton<SignalRClientService>();
        //builder.Services.AddSingleton<MessageHubClientService>(); 

        var dbPath = Path.Combine(FileSystem.AppDataDirectory, "deep_v4e.db");
        builder.Services.AddDbContext<AppDbContext>(options =>
            options.UseSqlite($"Filename={dbPath}"));

//#if DEBUG
        builder.Logging
        .ClearProviders()
        .AddDebug() // Output to logcat
        .AddConsole() // Output to console
        .SetMinimumLevel(LogLevel.Debug) // Global minimum level
        .AddFilter((category, level) =>
        {
            // Suppress EF Core and noisy stuff
            if (category.StartsWith("Microsoft.EntityFrameworkCore"))
                return false;
            if (category.StartsWith("Microsoft"))
                return false;
            if (category.StartsWith("System"))
                return false;

            // Only allow your app's namespace logs
            return category.StartsWith("DeepMessage");
        });
//#endif
        HttpClientHandler insecureHandler = GetInsecureHandler();

        builder.Services.AddHttpClient<BaseHttpClient, HttpTokenClient>("ServerAPI", client =>
        {

            client.BaseAddress = new Uri(APIUrl);


        }).ConfigurePrimaryHttpMessageHandler(() => insecureHandler);
        ;
        builder.Services.AddScoped<ILocalStorageService, LocalStorage>();

        // Register encryption service
        builder.Services.AddScoped<Platform.Client.Services.Services.IClientEncryptionService, Platform.Client.Services.Services.ClientEncryptionService>();
        builder.Services.AddSingleton<Platform.Client.Services.Services.ISecureKeyManager, Platform.Client.Services.Services.SecureKeyManager>();
        builder.Services.AddScoped<Platform.Client.Services.Services.IPasswordPromptService, Platform.Client.Services.Services.PasswordPromptService>();

        builder.Services.AddScoped<ISignupFormDataService, SignupClientSideFormDataService>();

        // Vertical slice pattern: Default (offline-first) and backup (online) services
        builder.Services.AddScoped<ISignInFormDataService, Platform.Client.Services.Features.Account.SigninForm.SignInOfflineFormDataService>();
        builder.Services.AddKeyedScoped<ISignInFormDataService, SigninClientSideFormDataService>("backup");
        builder.Services.AddScoped<IProfileListingDataService, ProfileClientSideListingDataService>();
        builder.Services.AddScoped<IProfileFormDataService, ProfileClientSideFormDataService>();
        builder.Services.AddScoped<IFriendFormDataService, FriendClientSideFormDataService>();
        builder.Services.AddScoped<FriendsClientSideListingDataService>();
        builder.Services.AddScoped<IFriendsListingDataService, FriendsOfflineListingDataService>();
        builder.Services.AddScoped<IStartChatFormDataService, StartChatClientSideFormDataService>();
        builder.Services.AddScoped<IChatThreadsListingDataService, ChatThreadsOfflineListingDataService>();
        builder.Services.AddScoped<ChatThreadsClientSideListingDataService>();
        builder.Services.AddScoped<IChatMessagesListingDataService, ChatMessagesClientSideListingDataService>();
        builder.Services.AddScoped<IChatMessageFormDataService, ChatMessageClientSideFormDataService>();
        builder.Services.AddScoped<IChatThreadSyncFormDataService, ChatThreadSyncClientSideFormDataService>();
        builder.Services.AddScoped<IChatMessagesSyncFormDataService, ChatMessagesSyncClientSideFormDataService>();
        builder.Services.AddScoped<IDeviceTokenFormDataService, DeviceTokenClientSideFormDataService>();
        builder.Services.AddScoped<IAuthCodeListingDataService, AuthCodeClientSideListingDataService>();
        builder.Services.AddScoped<IAuthCodeFormDataService, AuthCodeClientSideFormDataService>();
 
        builder.Services.AddSingleton(_ => CrossFirebaseCloudMessaging.Current);

        var app = builder.Build();
        var scope = app.Services.CreateScope();
        var services = scope.ServiceProvider;
        var dbContext = services.GetRequiredService<AppDbContext>();
        dbContext.Database.Migrate();
        services.GetRequiredService<ChatSyncUpService>().Start();
        services.GetRequiredService<SignalRClientService>().Start(ChatHubUrl);

        return app;
    }

    private static MauiAppBuilder RegisterFirebaseServices(this MauiAppBuilder builder)
    {
        builder.ConfigureLifecycleEvents(events =>
        {
#if IOS
            events.AddiOS(iOS => iOS.WillFinishLaunching((_,__) => {
                CrossFirebase.Initialize();
                return false;
            }));
#elif ANDROID
           
            events.AddAndroid(android => android.OnCreate((activity, _) =>
                CrossFirebase.Initialize(activity)));
            CrossFirebaseCrashlytics.Current.SetCrashlyticsCollectionEnabled(true);

#endif
        });
        CrossFirebaseCloudMessaging.Current.CheckIfValidAsync();
        //builder.Services.AddSingleton(_ => CrossFirebaseAuth.Current);
        CrossFirebaseCloudMessaging.Current.NotificationReceived += Current_NotificationReceived;
        return builder;
    }

    private static void Current_NotificationReceived(object? sender, Plugin.Firebase.CloudMessaging.EventArgs.FCMNotificationReceivedEventArgs e)
    {

    }

    public static HttpClientHandler GetInsecureHandler()
    {
        HttpClientHandler handler = new HttpClientHandler();
        handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) =>
        {
            if (cert.Issuer.Equals("CN=localhost"))
                return true;
            return errors == System.Net.Security.SslPolicyErrors.None;
        };
        return handler;
    }
}
