@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.AuthCodes
@using Platform.Client.Services.Features.AuthCodes
@using Platform.Framework.Core
@page "/referral-codes"
@inherits ListingBase<AuthCodeListingViewModel, AuthCodeListingBusinessObject, AuthCodeFilterViewModel, AuthCodeFilterBusinessObject, IAuthCodeListingDataService>

<!-- Referral Codes Management - WhatsApp Style -->
<div class="flex flex-col h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <button @onclick="NavigateBack" 
                        class="mr-3 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <svg class="w-6 h-6 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </button>
                <h1 class="text-xl font-semibold text-gray-900 dark:text-white">Referral Codes</h1>
            </div>
            <button @onclick="GenerateNewCode" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                Generate
            </button>
        </div>
    </div>

    <!-- Content -->
    <div class="flex-1 overflow-y-auto">
        @if (IsWorking)
        {
            <!-- Loading State -->
            <div class="flex items-center justify-center h-64">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        }
        else if (!string.IsNullOrEmpty(Error))
        {
            <!-- Error State -->
            <div class="p-4">
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <div class="flex">
                        <svg class="w-5 h-5 text-red-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error loading referral codes</h3>
                            <p class="mt-1 text-sm text-red-700 dark:text-red-300">@Error</p>
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (Items == null || !Items.Any())
        {
            <!-- Empty State -->
            <div class="flex flex-col items-center justify-center h-64 px-4">
                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No referral codes yet</h3>
                <p class="text-gray-500 dark:text-gray-400 text-center mb-4">Generate your first referral code to invite friends to the app</p>
                <button @onclick="GenerateNewCode" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200">
                    Generate First Code
                </button>
            </div>
        }
        else
        {
            <!-- Referral Codes List -->
            <div class="p-4 space-y-3">
                @foreach (var referralCode in Items)
                {
                    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <!-- Code Display -->
                                <div class="flex items-center mb-2">
                                    <div class="bg-gray-100 dark:bg-gray-700 rounded-lg px-3 py-2 font-mono text-lg font-bold text-gray-900 dark:text-white mr-3">
                                        @referralCode.AuthCode
                                    </div>
                                    <button @onclick="() => CopyToClipboard(referralCode.AuthCode)"
                                            class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                                            title="Copy code">
                                        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                        </svg>
                                    </button>
                                </div>

                                <!-- Status and Timing -->
                                <div class="flex items-center space-x-4 text-sm">
                                    <div class="flex items-center">
                                        <span class="@GetStatusClass(referralCode.AuthCodeStatus) px-2 py-1 rounded-full text-xs font-medium">
                                            @GetStatusText(referralCode.AuthCodeStatus)
                                        </span>
                                    </div>
                                    <div class="text-gray-500 dark:text-gray-400">
                                        Created: @referralCode.CreatedAt.ToString("MMM dd, HH:mm")
                                    </div>
                                    @if (referralCode.AuthCodeStatus == DeepMessage.ServiceContracts.Enums.AuthCodeStatus.Unused)
                                    {
                                        <div class="text-orange-600 dark:text-orange-400">
                                            Expires: @referralCode.ExpiresAt.ToString("MMM dd, HH:mm")
                                        </div>
                                    }
                                    @if (referralCode.ConsumedAt.HasValue)
                                    {
                                        <div class="text-green-600 dark:text-green-400">
                                            Used: @referralCode.ConsumedAt.Value.ToString("MMM dd, HH:mm")
                                        </div>
                                    }
                                </div>

                                @if (!string.IsNullOrEmpty(referralCode.ConsumedBy))
                                {
                                    <div class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                                        Used by: @referralCode.ConsumedBy
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
    </div>

    <!-- Info Section -->
    <div class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
            <div class="flex">
                <svg class="w-5 h-5 text-blue-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <div>
                    <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200">About Referral Codes</h4>
                    <p class="mt-1 text-sm text-blue-700 dark:text-blue-300">
                        Share these codes with friends to invite them to the app. Each code expires in 5 minutes and can only be used once.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
