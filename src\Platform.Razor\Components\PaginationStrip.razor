﻿@using DeepMessage.Framework.Core 
@using Microsoft.AspNetCore.Components.Forms;
@using Microsoft.JSInterop
@inherits InputSelect<PaginationStripModel>
<style>
    a {
        cursor: pointer;
    }
</style>


@{
    int n = 7;

    var totalPages = Convert.ToInt32(Math.Ceiling(TotalRows / (double)CurrentValue.RowsPerPage));
    if (totalPages < TotalPages)
    {
        TotalPages = totalPages;
    }

    List<int> all = new List<int>(TotalPages);
    for (int i = 1; i <= TotalPages; i++)
    {
        if (i != CurrentIndex)
            all.Add(i);
    }

    List<int> pages = new List<int>(n);

    pages.Add(CurrentValue.RowsPerPage >= TotalRows ? 1 : CurrentIndex);
    for (int i = 1; i <= n / 2; i++)
    {
        var u = all.FirstOrDefault(x => x > CurrentIndex);
        if (u > 0)
        {
            pages.Add(u);
            all.Remove(u);
        }
        else
        {
            var l = all.LastOrDefault(x => x < CurrentIndex);
            if (l > 0)
            {
                if (!pages.Any(p => p == l))
                {
                    pages.Add(l);
                }

                all.Remove(l);
            }
        }


        var u1 = all.LastOrDefault(x => x < CurrentIndex);
        if (u1 > 0)
        {
            pages.Add(u1);
            all.Remove(u1);
        }
        else
        {
            var l1 = all.FirstOrDefault(x => x > CurrentIndex);
            if (l1 > 0)
            {
                pages.Add(l1);
                all.Remove(l1);
            }
        }
        pages.Sort();
    }
}
@if (TotalRows > 10)
{

    <div class="relative overflow-hidden bg-white rounded-lg">
        <nav class="pagination-container" aria-label="Page navigation">
            <span class="results">
                @PaginationStats(CurrentIndex)
            </span>
            <ul class="pagination">
                <li>
                    <a class="previous" href="javascript:;" @onclick="async () => await UpdatePage(Math.Max(CurrentIndex - 1, 1))">
                        <span class="sr-only">Previous</span>
                        <i class="text-sm fa-solid fa-angle-left rtl:rotate-180"></i>
                    </a>
                </li>
                <li>
                    <a href="javascript:;" @onclick="async () => await UpdatePage(Math.Max(CurrentIndex - n - 1, 1))" class="page-link">...</a>
                </li>

                @foreach (int i in pages)
                {
                    int c = i;
                    var activeClass = CurrentIndex == c ? "active" : null;

                    <li>
                        <a x-data x-on:click="scrollIntoView()" @onclick="async () => await UpdatePage(c)" class="page-link @activeClass" @attributes="@(CurrentIndex == c ? areaCurrentAttribute : nonAttribute)">@c</a>
                    </li>
                }
                <li>
                    <a href="javascript:;" @onclick="async () => await UpdatePage(Math.Min(CurrentIndex + n - 1, TotalPages))" class="page-link">...</a>
                </li>

                <li>
                    <a href="javascript:;" @onclick="async () => await UpdatePage(Math.Min(CurrentIndex + 1, TotalPages))" class="next">
                        <span class="sr-only">Next</span>
                        <i class="text-sm fa-solid fa-angle-right rtl:rotate-180"></i>
                    </a>
                </li>
            </ul>
            <InputSelect TValue="int" @bind-Value="CurrentValue.RowsPerPage">
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
                <option value="@TotalRows">All</option>
            </InputSelect>
        </nav>
    </div>


}
