﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Server.DataServices.Helpers;
using DeepMessage.Server.DataServices.Migrations;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Hybrid;
namespace DeepMessage.Server.DataServices.Features.Conversation;
public class ChatMessagesSyncServerSideFormDataService : IChatMessagesSyncFormDataService
{

    private readonly AppDbContext _context;
    private readonly HybridCache hybridCache;
    private readonly MessageDispatcher messageDispatcher;

    public ChatMessagesSyncServerSideFormDataService(AppDbContext context,
        HybridCache hybridCache,
        MessageDispatcher messageDispatcher)
    {
        _context = context;
        this.hybridCache = hybridCache;
        this.messageDispatcher = messageDispatcher;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(ChatMessagesSyncFormBusinessObject formBusinessObject)
    {

        var message = _context.Messages.FirstOrDefault(x => x.Id == formBusinessObject.Id);
        if (message == null)
        {
            message = new Message()
            {
                Id = formBusinessObject.Id,
                ConversationId = formBusinessObject.ConversationId,
                CreatedAt = formBusinessObject.CreatedAt,
                DeletedAt = formBusinessObject.DeletedAt,
                DisappearAfter = formBusinessObject.DisappearAfter,
                DisappearAt = formBusinessObject.DisappearAt,
                IsDeleted = formBusinessObject.IsDeleted,
                IsEdited = formBusinessObject.IsEdited,
                IsEphemeral = formBusinessObject.IsEphemeral,
                EditedAt = formBusinessObject.EditedAt,
                PlainContent = formBusinessObject.PlainContent,
                SenderId = formBusinessObject.SenderId,
                //todo: sender device id for multi device support
                DeliveryStatus = ServiceContracts.Enums.DeliveryStatus.SentToMessageServer,
                DeliveryStatusTime = DateTime.UtcNow,
            };
            _context.Messages.Add(message);
            await _context.SaveChangesAsync();

            var conversationParticipants = await _context.ConversationParticipants.Where(x => x.ConversationId == message.ConversationId
                && x.UserId != formBusinessObject.SenderId).ToListAsync();

            foreach (var participant in conversationParticipants)
            {
                var messageRecipient = await _context.MessageRecipients.FirstOrDefaultAsync(x => x.MessageId == message.Id && x.RecipientId == participant.Id);
                if (messageRecipient == null)
                {
                    messageRecipient = new MessageRecipient()
                    {
                        Id = Guid.CreateVersion7().ToString().ToLower(),
                        MessageId = message.Id,
                        UserDeviceId = participant.UserId,
                        RecipientId = participant.UserId,
                        EncryptedContent = message.PlainContent ?? string.Empty,
                        DeliveryStatus = ServiceContracts.Enums.DeliveryStatus.SentToMessageServer,
                        DeliveryStatusTime = DateTime.UtcNow,
                    };
                }
                _context.MessageRecipients.Add(messageRecipient);
                await _context.SaveChangesAsync();

            }
        }

        var pendingMessages = (from m in _context.Messages
                               where m.Id == message.Id
                               select new ChatMessagesSyncFormBusinessObject
                               {
                                   Id = m.Id,
                                   ConversationId = m.ConversationId,
                                   SenderId = m.SenderId,
                                   CreatedAt = m.CreatedAt,
                                   DeletedAt = m.DeletedAt,
                                   DisappearAfter = m.DisappearAfter,
                                   DisappearAt = m.DisappearAt,
                                   EditedAt = m.EditedAt,
                                   IsDeleted = m.IsDeleted,
                                   IsEdited = m.IsEdited,
                                   IsEphemeral = m.IsEphemeral,
                                   PlainContent = m.PlainContent,
                                   EnableFallBackChannel = true,
                               }).ToList();
        foreach (var item in pendingMessages)
        {
            messageDispatcher.DispatchMessage(item);
        }
        return message.Id;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<ChatMessagesSyncFormBusinessObject> GetItemByIdAsync(string id)
    {
        var query = (from m in _context.Messages
                     where m.Id == id
                     select new ChatMessagesSyncFormBusinessObject()
                     {
                         Id = m.Id,
                         ConversationId = m.ConversationId,
                         SenderId = m.SenderId,
                         CreatedAt = m.CreatedAt,
                         DeletedAt = m.DeletedAt,
                         DisappearAfter = m.DisappearAfter,
                         DisappearAt = m.DisappearAt,
                         EditedAt = m.EditedAt,
                         IsDeleted = m.IsDeleted,
                         IsEdited = m.IsEdited,
                         IsEphemeral = m.IsEphemeral,
                         PlainContent = m.PlainContent
                     });
        var chatMessage = await query.FirstAsync();
        return chatMessage;
    }
}
