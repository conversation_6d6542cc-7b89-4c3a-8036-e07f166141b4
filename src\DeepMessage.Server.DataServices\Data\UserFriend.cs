﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DeepMessage.Server.DataServices.Data
{
    /// <summary>
    /// Represents the friendship relationship once two users become friends.
    /// You can store blocked/removed states here or keep it minimal.
    /// </summary>
    public class Friendship
    {
        [Key, StringLength(450)]
        public string Id { get; set; } = null!;

        // Typically, you store each pair of friends in both directions 
        // or enforce a single row for both. 
        // A simple approach is two rows (one for each direction).
        // Alternatively, if you want a single row for a mutual friendship, 
        // you can store (UserAId, UserBId) as a unique pair and handle symmetrical logic.

        public string UserId { get; set; } = null!;
        public ApplicationUser User { get; set; } = null!;

        public string FriendId { get; set; } = null!;
        public ApplicationUser Friend { get; set; } = null!;

        [StringLength(450)]
        public string Name { get; set; } = string.Empty;

        [StringLength(450)]
        public string? DisplayPictureUrl { get; set; }

        public bool SendReadReceipts { get; set; }

        public bool ShowOnlineStatus { get; set; }

        // If you want to track the date/time they became friends
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // If you want to handle block/unblock
        public bool IsBlocked { get; set; } = false;
        public DateTime? BlockedAt { get; set; }

        // Soft delete, in case you want to keep a record
        public bool IsDeleted { get; set; } = false;
        public DateTime? DeletedAt { get; set; }
    }

    /// <summary>
    /// Represents a friend request flow, which can be an invite code or an approval flow.
    /// - If using an invite/pin code, fill PinCode & ExpiresAt
    /// - If using a direct "send friend request," track the request/approval in Status
    /// </summary>
    public class FriendRequest
    {
        [Key, StringLength(450)]
        public string Id { get; set; } = null!;

        // The user who initiated the request or generated the code
        public string UserId { get; set; } = null!;
        public ApplicationUser User { get; set; } = null!;

        public FriendRequestStatus Status { get; set; } = FriendRequestStatus.Pending;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [StringLength(450)]
        public string? PinCode { get; set; }
        
        public DateTime? ExpiresAt { get; set; }

        // Timestamps for transitions
        public DateTime? ApprovedAt { get; set; }

        public DateTime? RejectedAt { get; set; }

        // For "Cancelled" state if the requestor revokes it
        public DateTime? CancelledAt { get; set; }
    }

    public enum FriendRequestStatus
    {
        Pending = 0,
        Approved = 1,
        Rejected = 2,
        Expired = 3,
        Cancelled = 4
    }
}
