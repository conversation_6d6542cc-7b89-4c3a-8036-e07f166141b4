{"Profiles": [{"Name": "DeepMessage", "FrameworkNamespaces": "DeepMessage.Framework.Core", "DbContextNamespaces": "DeepMessage.Server.DataServices.Data", "Projects": [{"Id": 1, "Name": "ServicesContract", "Path": "DeepMessage.ServiceContracts\\Features", "NameSpace": "DeepMessage.ServiceContracts.Features", "ParentId": 0, "ProjectType": 2}, {"Id": 2, "Name": "ServersideDataServices", "Path": "DeepMessage.Server.DataServices\\Features", "NameSpace": "DeepMessage.Server.DataServices.Features", "ParentId": 1, "ProjectType": 3}, {"Id": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Path": "Platform.Client.Common\\Features", "NameSpace": "Platform.Client.Common.Features", "ParentId": 1, "ProjectType": 6}, {"Id": 4, "Name": "Controllers", "Path": "DeepMessage.Server.WebApis\\Controllers", "NameSpace": "DeepMessage.Server.Controllers", "ParentId": 1, "ProjectType": 5}, {"Id": 5, "Name": "ClientServices", "Path": "Platform.Client.Services\\Features", "NameSpace": "Platform.Client.Services.Features", "ParentId": 1, "ProjectType": 9}, {"Id": 6, "Name": "RazorComponents", "Path": "Platform.Razor\\Features", "NameSpace": "Platform.Razor.Features", "ParentId": 1, "ProjectType": 10}]}], "ProjectFiles": {"ServiceContracts": [{"FileName": "Listing\\I##ComponentPrefix##ListingDataService.cs", "FileCategory": 1, "Content": ["using ##frmaeworkNamespace##;", "namespace DeepMessage.ServiceContracts.Features.##moduleNamespace##;", "public interface I##ComponentPrefix##ListingDataService :", "\tIListingDataService<##ComponentPrefix##ListingBusinessObject, ##ComponentPrefix##FilterBusinessObject>", "{", "\t//Add any custom methods here", "}"]}, {"FileName": "Listing\\##ComponentPrefix##ListingBusinessObject.cs", "FileCategory": 1, "Content": ["using ##frmaeworkNamespace##;", "namespace DeepMessage.ServiceContracts.Features.##moduleNamespace##;", "public class ##ComponentPrefix##ListingBusinessObject", "{", "\t//Add Listing BusinessObject Attributes here", "}"]}, {"FileName": "Listing\\##ComponentPrefix##FilterBusinessObject.cs", "FileCategory": 1, "Content": ["using ##frmaeworkNamespace##;", "namespace DeepMessage.ServiceContracts.Features.##moduleNamespace##;", "public class ##ComponentPrefix##FilterBusinessObject :  BaseFilterBusinessObject", "{", "\t//Add Filter BusinessObject Attributes here", "}"]}, {"FileName": "Form\\I##ComponentPrefix##FormDataService.cs", "FileCategory": 2, "Content": ["using ##frmaeworkNamespace##;", "namespace DeepMessage.ServiceContracts.Features.##moduleNamespace##;", "public interface I##ComponentPrefix##FormDataService :", "\tIFormDataService<##ComponentPrefix##FormBusinessObject, ##primaryKeyType##>", "{", "\t//Add any custom methods here", "}"]}, {"FileName": "Form\\##ComponentPrefix##FormBusinessObject.cs", "FileCategory": 2, "Content": ["using ##frmaeworkNamespace##;", "namespace DeepMessage.ServiceContracts.Features.##moduleNamespace##;", "public class ##ComponentPrefix##FormBusinessObject", "{", "\t//Add Form BusinessObject Attributes here", "}"]}], "ServerSideServices": [{"FileName": "Listing\\##ComponentPrefix##ServerSideListingDataService.cs", "FileCategory": 1, "Content": ["using ##dbContextNamespace##;", "using DeepMessage.Framework.Enums;", "using DeepMessage.ServiceContracts.Features.##moduleNamespace##;", "namespace DeepMessage.Server.DataServices.Features.##moduleNamespace##;", "public class ##ComponentPrefix##ServerSideListingDataService : ServerSideListingDataService<##ComponentPrefix##ListingBusinessObject, ##ComponentPrefix##FilterBusinessObject>, I##ComponentPrefix##ListingDataService", "{", "", "\t//private readonly AppDbContext _context;", "", "\t//public ##ComponentPrefix##ServerSideListingDataService (AppDbContext context)", "\t//{", "\t\t//_context = context;", "\t//}", "\t[SystemClaim(SystemClaimType.##FeatureEnum##)]", "\tpublic override IQueryable<##ComponentPrefix##ListingBusinessObject> GetQuery(##ComponentPrefix##FilterBusinessObject filterBusinessObject)", "\t{", "\t\tthrow new NotImplementedException();", "\t}", "}"]}, {"FileName": "Form\\##ComponentPrefix##ServerSideFormDataService.cs", "FileCategory": 2, "Content": ["using ##frmaeworkNamespace##;", "using ##dbContextNamespace##;", "using DeepMessage.Framework.Enums;", "using DeepMessage.ServiceContracts.Features.##moduleNamespace##;", "namespace DeepMessage.Server.DataServices.Features.##moduleNamespace##;", "public class ##ComponentPrefix##ServerSideFormDataService : I##ComponentPrefix##FormDataService", "{", "", "\tprivate readonly AppDbContext _context;", "", "\tpublic ##ComponentPrefix##ServerSideFormDataService (AppDbContext context)", "\t{", "\t\t_context = context;", "\t}", "\t[SystemClaim(SystemClaimType.##FeatureEnum##)]", "\tpublic async Task<##primaryKeyType##> SaveAsync(##ComponentPrefix##FormBusinessObject formBusinessObject)", "\t{", "\t\tthrow new NotImplementedException();", "\t}", "\t[SystemClaim(SystemClaimType.##FeatureEnum##)]", "\tpublic async Task<##ComponentPrefix##FormBusinessObject?> GetItemByIdAsync(##primaryKeyType## id)", "\t{", "\t\tthrow new NotImplementedException();", "\t}", "}"]}], "Controllers": [{"FileName": "##ComponentPrefix##ListingController.cs", "FileCategory": 1, "Content": ["using Microsoft.AspNetCore.Mvc;", "using ##frmaeworkNamespace##;", "using DeepMessage.Framework.Enums;", "using DeepMessage.ServiceContracts.Features.##moduleNamespace##;", "namespace DeepMessage.Server.WebApis.Controller.##moduleNamespace##;", "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>(\"{culture:culture}/api/[controller]/[action]\")]", "public class ##ComponentPrefix##ListingController : ControllerB<PERSON>, I##ComponentPrefix##ListingDataService", "{", "", "\tprivate readonly I##ComponentPrefix##ListingDataService dataService;", "", "\tpublic ##ComponentPrefix##ListingController(I##ComponentPrefix##ListingDataService dataService)", "\t{", "\t\tthis.dataService = dataService;", "\t}", "\t[SystemClaim(SystemClaimType.##FeatureEnum##)]", "\t[HttpGet]", "\tpublic async Task<PagedDataList<##ComponentPrefix##ListingBusinessObject>> GetPaginatedItems([FromQuery] ##ComponentPrefix##FilterBusinessObject businessObject)", "\t{", "\t\treturn await dataService.GetPaginatedItems(businessObject);", "\t}", "}"]}, {"FileName": "##ComponentPrefix##sFormController.cs", "FileCategory": 1, "Content": ["using Microsoft.AspNetCore.Mvc;", "using DeepMessage.Framework.Enums;", "using ##frmaeworkNamespace##;", "using DeepMessage.ServiceContracts.Features.##moduleNamespace##;", "namespace DeepMessage.Server.WebApis.Controller.##moduleNamespace##;", "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>(\"{culture:culture}/api/[controller]/[action]\")]", "public class ##ComponentPrefix##sFormController : ControllerB<PERSON>, I##ComponentPrefix##FormDataService", "{", "", "\tprivate readonly I##ComponentPrefix##FormDataService dataService;", "", "\tpublic ##ComponentPrefix##sFormController(I##ComponentPrefix##FormDataService dataService)", "\t{", "\t\tthis.dataService = dataService;", "\t}", "\t[SystemClaim(SystemClaimType.##FeatureEnum##)]", "\t[HttpPost]", "\tpublic async Task<##primaryKeyType##> SaveAsync([FromBody] ##ComponentPrefix##FormBusinessObject formBusinessObject)", "\t{", "\t\treturn await dataService.SaveAsync(formBusinessObject);", "\t}", "\t[SystemClaim(SystemClaimType.##FeatureEnum##)]", "\t[HttpGet]", "\tpublic async Task<##ComponentPrefix##FormBusinessObject?> GetItemByIdAsync(##primaryKeyType## id)", "\t{", "\t\treturn await dataService.GetItemByIdAsync(id);", "\t}", "}"]}], "UILibrary": [{"FileName": "Listing\\##ComponentPrefix##ListingComponent.xaml", "FileCategory": 1, "Content": ["<?xml version=\"1.0\" encoding=\"utf-8\" ?>", "<local:##ComponentPrefix##ListingViewBase xmlns=\"http://schemas.microsoft.com/dotnet/2021/maui\"", "             xmlns:x=\"http://schemas.microsoft.com/winfx/2009/xaml\"", "             x:Class=\"Platform.Client.Common.Features.##moduleNamespace##.##ComponentPrefix##ListingView\"", "             xmlns:local=\"clr-namespace:Platform.Client.Common.Features.##moduleNamespace##\"", "             Title=\"##ComponentPrefix##ListingView\">", "    <VerticalStackLayout>", "        <Label ", "            Text=\"Welcome to .NET MAUI!\"", "            VerticalOptions=\"Center\" ", "            HorizontalOptions=\"Center\" />", "    </VerticalStackLayout>", "</local:##ComponentPrefix##ListingViewBase>"]}, {"FileName": "Listing\\##ComponentPrefix##ListingComponent.xaml.cs", "FileCategory": 1, "Content": ["using DeepMessage.MauiShared;", "using DeepMessage.ServiceContracts.Features.##moduleNamespace##;", "using Platform.Client.Services.Features.##moduleNamespace##;", "namespace Platform.Client.Common.Features.##moduleNamespace##;", "public class ##ComponentPrefix##ListingViewBase : ListingBaseMaui<##ComponentPrefix##ListingViewModel,##ComponentPrefix##ListingBusinessObject,##ComponentPrefix##FilterViewModel,##ComponentPrefix##FilterBusinessObject, I##ComponentPrefix##ListingDataService>", "{", "\tpublic ##ComponentPrefix##ListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)", "\t{", "\t}", "}", "\n", "public partial class ##ComponentPrefix##ListingView : ##ComponentPrefix##ListingViewBase", "{", "\tpublic ##ComponentPrefix##ListingView(IServiceScopeFactory scopeFactory) : base(scopeFactory)", "\t{", "\tInitializeComponent();", "\t}", "}", "", ""]}, {"FileName": "Form\\##ComponentPrefix##FormComponent.xaml", "FileCategory": 1, "Content": ["<?xml version=\"1.0\" encoding=\"utf-8\" ?>", "<local:##ComponentPrefix##FormViewBase xmlns=\"http://schemas.microsoft.com/dotnet/2021/maui\"", "             xmlns:x=\"http://schemas.microsoft.com/winfx/2009/xaml\"", "             x:Class=\"Platform.Client.Common.Features.##moduleNamespace##.##ComponentPrefix##FormView\"", "             xmlns:local=\"clr-namespace:Platform.Client.Common.Features.##moduleNamespace##\"", "             Title=\"##ComponentPrefix##FormView\">", "    <VerticalStackLayout>", "        <Label ", "            Text=\"Welcome to .NET MAUI!\"", "            VerticalOptions=\"Center\" ", "            HorizontalOptions=\"Center\" />", "    </VerticalStackLayout>", "</local:##ComponentPrefix##FormViewBase>"]}, {"FileName": "Form\\##ComponentPrefix##FormComponent.xaml.cs", "FileCategory": 2, "Content": ["using DeepMessage.MauiShared;", "using DeepMessage.ServiceContracts.Features.##moduleNamespace##;", "using Platform.Client.Services.Features.##moduleNamespace##;", "namespace Platform.Client.Common.Features.##moduleNamespace##;", "public class ##ComponentPrefix##FormViewBase : FormBaseMaui<##ComponentPrefix##FormBusinessObject, ##ComponentPrefix##FormViewModel, ##primaryKeyType##, I##ComponentPrefix##FormDataService>", "{", "    public ##ComponentPrefix##FormViewBase(IServiceScopeFactory scopeFactory, ##primaryKeyType## key) : base(scopeFactory, key)", "    {", "    }", "}", "", "public partial class ##ComponentPrefix##FormView : ##ComponentPrefix##FormViewBase", "{", "    public ##ComponentPrefix##FormView(IServiceScopeFactory scopeFactory, ##primaryKeyType## key) : base(scopeFactory, key)", "    {", "        InitializeComponent();", "        BindingContext = this;", "    }", "}"]}], "ClientDependency": [{"FileName": "ServiceRegistrar.cs", "FileCategory": 1, "Content": []}], "ServerSideDependency": [{"FileName": "ServiceRegistrar.cs", "FileCategory": 1, "Content": []}], "ClientServices": [{"FileName": "Listing\\##ComponentPrefix##ListingViewModel.cs", "FileCategory": 1, "Content": ["using ##frmaeworkNamespace##;", "namespace ##projectNamespace##;", "public class ##ComponentPrefix##ListingViewModel", "{", "\t//Add Listing ViewModel Attributes here", "}"]}, {"FileName": "Listing\\##ComponentPrefix##FilterViewModel.cs", "FileCategory": 1, "Content": ["using ##frmaeworkNamespace##;", "namespace ##projectNamespace##;", "public class ##ComponentPrefix##FilterViewModel :  BaseFilterViewModel", "{", "\t//Add Filter ViewModel Attributes here", "}"]}, {"FileName": "Form\\##ComponentPrefix##FormViewModel.cs", "FileCategory": 2, "Content": ["using ##frmaeworkNamespace##;", "namespace ##projectNamespace##;", "public class ##ComponentPrefix##FormViewModel : ObservableBase", "{", "\t//Add Form Model Attributes here", "}"]}, {"FileName": "Listing\\##ComponentPrefix##ClientSideListingDataService.cs", "FileCategory": 1, "Content": ["using DeepMessage.Framework.Enums;", "using ##frmaeworkNamespace##;", "using DeepMessage.MauiShared;", "using DeepMessage.MauiApp.Helpers;", "using DeepMessage.ServiceContracts.Features.##moduleNamespace##;", "namespace ##projectNamespace##;", "public class ##ComponentPrefix##ClientSideListingDataService : I##ComponentPrefix##ListingDataService", "{", "", "\tprivate readonly BaseHttpClient _httpClient;", "", "\tpublic ##ComponentPrefix##ClientSideListingDataService (BaseHttpClient context)", "\t{", "\t\t_httpClient = context;", "\t}", "\t[SystemClaim(SystemClaimType.##FeatureEnum##)]", "\tpublic async Task<PagedDataList<##ComponentPrefix##ListingBusinessObject>> GetPaginatedItems(##ComponentPrefix##FilterBusinessObject filterBusinessObject)", "\t{", "\t\treturn await _httpClient.GetFromJsonAsync<PagedDataList<##ComponentPrefix##ListingBusinessObject>>($\"api/##ComponentPrefix##Listing/GetPaginatedItems\" + filterBusinessObject.ToQueryString());", "\t}", "}"]}, {"FileName": "Form\\##ComponentPrefix##ClientSideFormDataService.cs", "FileCategory": 2, "Content": ["using System.Text;", "using System.Text.Json;", "using System.Net.Http.Json;", "using DeepMessage.Framework.Enums;", "using ##frmaeworkNamespace##;", "using DeepMessage.ServiceContracts;", "using DeepMessage.MauiShared;", "using DeepMessage.ServiceContracts.Features.##moduleNamespace##;", "namespace ##projectNamespace##;", "public class ##ComponentPrefix##ClientSideFormDataService : I##ComponentPrefix##FormDataService", "{", "", "\tprivate readonly BaseHttpClient _httpClient;", "", "\tpublic ##ComponentPrefix##ClientSideFormDataService (BaseHttpClient context)", "\t{", "\t\t_httpClient = context;", "\t}", "\t[SystemClaim(SystemClaimType.##FeatureEnum##)]", "\tpublic async Task<##primaryKeyType##> SaveAsync(##ComponentPrefix##FormBusinessObject formBusinessObject)", "\t{", "\t\t return await _httpClient.PostAsJsonAsync<##primaryKeyType##>($\"api/##ComponentPrefix##sForm/Save\", formBusinessObject);", "\t}", "\t[SystemClaim(SystemClaimType.##FeatureEnum##)]", "\tpublic async Task<##ComponentPrefix##FormBusinessObject?> GetItemByIdAsync(##primaryKeyType## id)", "\t{", "\t\treturn await _httpClient.GetFromJsonAsync<##ComponentPrefix##FormBusinessObject>($\"api/##ComponentPrefix##sForm/GetItemById?id=\" + id);", "\t}", "}"]}], "RazorComponents": [{"FileName": "Listing\\##ComponentPrefix##Listing.razor", "FileCategory": 1, "Content": ["@inherits ListingBase<##ComponentPrefix##ListingViewModel,##ComponentPrefix##ListingBusinessObject,##ComponentPrefix##FilterViewModel,##ComponentPrefix##FilterBusinessObject, I##ComponentPrefix##ListingDataService>", "@using DeepMessage.ServiceContracts.Features.##moduleNamespace##", "@using Platform.Client.Services.Features.##moduleNamespace##", "", "<div class=\"listing-container\">", "    @if (IsWorking)", "    {", "        <div class=\"loading\">Loading...</div>", "    }", "    else if (!string.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(Error))", "    {", "        <div class=\"error\">@Error</div>", "    }", "    else", "    {", "        <div class=\"items\">", "            @foreach (var item in Items)", "            {", "                <div class=\"item\">", "                    <!-- Add your listing item template here -->", "                    @item", "                </div>", "            }", "        </div>", "        ", "        @if (UsePagination)", "        {", "            <div class=\"pagination\">", "                <button @onclick=\"PreviousPage\" disabled=\"@(CurrentPage <= 1)\">Previous</button>", "                <span>Page @CurrentPage of @Math.Ceiling((double)TotalRecords / PageSize)</span>", "                <button @onclick=\"NextPage\" disabled=\"@(CurrentPage >= Math.Ceiling((double)TotalRecords / PageSize))\">Next</button>", "            </div>", "        }", "    }", "</div>"]}, {"FileName": "Form\\##ComponentPrefix##Form.razor", "FileCategory": 2, "Content": ["@inherits FormBase<##ComponentPrefix##FormBusinessObject,##ComponentPrefix##FormViewModel, ##primaryKeyType##, I##ComponentPrefix##FormDataService>", "@using DeepMessage.ServiceContracts.Features.##moduleNamespace##", "@using Platform.Client.Services.Features.##moduleNamespace##", "", "<div class=\"form-container\">", "    @if (IsWorking)", "    {", "        <div class=\"loading\">Loading...</div>", "    }", "    else if (!string.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(Error))", "    {", "        <div class=\"error\">@Error</div>", "    }", "    else", "    {", "        <EditForm Model=\"SelectedItem\" OnValidSubmit=\"HandleFormSubmit\">", "            <DataAnnotationsValidator />", "            <ValidationSummary />", "            ", "            <!-- Add your form fields here -->", "            <div class=\"form-group\">", "                <label>Sample Field:</label>", "                <InputText @bind-Value=\"SelectedItem.ToString()\" class=\"form-control\" />", "            </div>", "            ", "            <div class=\"form-actions\">", "                <button type=\"submit\" class=\"btn btn-primary\" disabled=\"@IsWorking\">Save</button>", "            </div>", "        </EditForm>", "    }", "</div>"]}]}}