using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Platform.Client.Services.Features.Conversation;
using Platform.Client.Services.Services;
using System.Security.Cryptography;
using System.Text;
using System.Timers;

namespace Platform.Razor.Features.Chat.Messages
{
    public partial class MessagesListing : IDisposable
    {
        [Parameter] public string ConversationId { get; set; } = string.Empty;

        [Inject] ISecureKeyManager secureKeyManager { get; set; } = null!;


        private ElementReference? messagesDiv;
        private System.Timers.Timer? _searchTimer;
        private const int SearchDelayMs = 500;
        private bool showSearch = false;

        // Participant information (would typically come from route parameters or service)
        public string ParticipantName { get; set; } = "Chat Participant";
        public string ParticipantAvatar { get; set; } = string.Empty;

        protected override async Task OnInitializedAsync()
        {
            // Set the conversation ID in the filter
            FilterViewModel.ConversationId = ConversationId;

            // Initialize the search timer for debounced search
            _searchTimer = new System.Timers.Timer(SearchDelayMs);
            _searchTimer.Elapsed += OnSearchTimerElapsed;
            _searchTimer.AutoReset = false;

            PubSub.Hub.Default.Subscribe<string>((m) =>
            {
                if (m == "NewMessageReceived")
                {
                    _ = LoadItems(false);
                }
            });
            await base.OnInitializedAsync();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            await base.OnAfterRenderAsync(firstRender);
            if (messagesDiv != null && JsRuntime != null)
            {
                try
                {
                    await JsRuntime.InvokeVoidAsync("initStickyBadges", messagesDiv);
                    await JsRuntime.InvokeVoidAsync("scrollIntoView", messagesDiv);
                }
                catch (Exception ex)
                {
                    Logger.LogWarning($"{ex.Message}");
                }
            }
        }

        protected override List<ChatMessagesListingViewModel> ConvertToListViewModel(List<ChatMessagesListingBusinessObject> list)
        {
            var orderedItems = new List<ChatMessagesListingViewModel>();
            foreach (var item in list)
            {
                orderedItems.Add(new ChatMessagesListingViewModel
                {
                    Id = item.Id,
                    Content = DecryptMessageContent(item.Content),
                    IsIncoming = item.IsIncoming,
                    Timestamp = item.Timestamp,
                    DeliveryStatus = item.DeliveryStatus,
                });
            }
            return orderedItems;
        }

        /// <summary>
        /// Decrypts message content using RSA private key from secure memory
        /// </summary>
        private string DecryptMessageContent(string? encryptedContent)
        {
            if (string.IsNullOrEmpty(encryptedContent))
                return string.Empty;

            try
            {
                // Check if RSA key is available in memory
                if (!secureKeyManager.IsRSAKeyAvailable())
                {
                    // If no key available, return placeholder indicating authentication needed
                    return "[Authentication required to decrypt message]";
                }

                // Get RSA private key from secure memory
                var rsaKey = Task.Run(() => secureKeyManager.GetRSAPrivateKeyAsync()).Result;
                if (rsaKey == null)
                {
                    return "[Failed to load decryption key]";
                }

                using (rsaKey)
                {
                    // Decrypt the message content
                    var encryptedBytes = Convert.FromBase64String(encryptedContent);
                    var decryptedBytes = rsaKey.Decrypt(encryptedBytes, RSAEncryptionPadding.OaepSHA256);
                    return Encoding.UTF8.GetString(decryptedBytes);
                }
            }
            catch (Exception ex)
            {
                // Log the error (in production, use proper logging)
                System.Diagnostics.Debug.WriteLine($"Failed to decrypt message: {ex.Message}");
                return "[Failed to decrypt message]";
            }
        }

        /// <summary>
        /// Handles search input with debouncing
        /// </summary>
        private void OnSearchKeyUp()
        {
            _searchTimer?.Stop();
            _searchTimer?.Start();
        }

        /// <summary>
        /// Timer elapsed event for debounced search
        /// </summary>
        private async void OnSearchTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            await InvokeAsync(async () =>
            {
                await LoadItems();
                StateHasChanged();
            });
        }

        /// <summary>
        /// Toggles the search bar visibility
        /// </summary>
        private void ToggleSearch()
        {
            showSearch = !showSearch;
            if (!showSearch)
            {
                FilterViewModel.SearchText = string.Empty;
                _ = LoadItems();
            }
        }

        /// <summary>
        /// Clears the search text and refreshes messages
        /// </summary>
        private async Task ClearSearch()
        {
            FilterViewModel.SearchText = string.Empty;
            await LoadItems();
        }

        /// <summary>
        /// Navigates back to chat threads list
        /// </summary>
        private void GoBack()
        {
            Navigation.NavigateTo("/chat");
        }

        /// <summary>
        /// Gets the initials from a name for avatar display
        /// </summary>
        private string GetInitials(string? name)
        {
            if (string.IsNullOrEmpty(name))
                return "?";

            var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 0)
                return "?";

            if (parts.Length == 1)
                return parts[0].Substring(0, Math.Min(2, parts[0].Length)).ToUpper();

            return $"{parts[0][0]}{parts[^1][0]}".ToUpper();
        }

        /// <summary>
        /// Formats the timestamp for message display
        /// </summary>
        private string GetFormattedTime(DateTime? timestamp)
        {
            if (!timestamp.HasValue)
                return "";

            var now = DateTime.UtcNow;
            var messageTime = timestamp.Value;

            if (messageTime.Date == now.Date)
                return messageTime.ToString("HH:mm");

            if (messageTime.Date == now.Date.AddDays(-1))
                return "Yesterday " + messageTime.ToString("HH:mm");

            if (messageTime.Year == now.Year)
                return messageTime.ToString("MMM dd HH:mm");

            return messageTime.ToString("MMM dd, yyyy HH:mm");
        }


        /// <summary>
        /// Handles scroll events for pagination
        /// </summary>
        private async Task OnScroll()
        {
            try
            {
                var scrollTop = await JsRuntime!.InvokeAsync<double>("getScrollTop", messagesDiv);

                // Load more messages when scrolled to top (for message history)
                //if (scrollTop <= 100 && !IsWorking && CurrentPage > 1)
                //{
                //    await PreviousPage();
                //}
            }
            catch (Exception)
            {
                // Ignore JavaScript errors during scroll handling
            }
        }

        private async Task OnMessageSent()
        {
            await LoadItems(false);
        }

        void IDisposable.Dispose()
        {
            _searchTimer.Stop();
            _searchTimer.Dispose();
        }
    }
}
