﻿using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.Client.Common.Data;
using System.Security.Claims;
using Microsoft.EntityFrameworkCore;
using DeepMessage.MauiApp.Services;
using Platform.Framework.Core;
namespace Platform.Client.Services.Features.Conversation.ChatThreads.Form;
public class StartChatClientSideFormDataService : IStartChatFormDataService
{

    private readonly AppDbContext context;
    private readonly ILocalStorageService localStorageService;
    private readonly ChatSyncUpService chatSyncUpService;

    public StartChatClientSideFormDataService(AppDbContext context, ILocalStorageService localStorageService, ChatSyncUpService chatThreadsSyncService)
    {
        this.context = context;
        this.localStorageService = localStorageService;
        this.chatSyncUpService = chatThreadsSyncService;
    }

    public string CombineGuidsXor(string guid1, string guid2)
    {
        string combined = string.Empty;
        for (int i = 0; i < guid1.Length; i++)
        {
            combined += (i % 2 == 0) ? guid1[i] : guid2[i];
        }

        return combined;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(StartChatFormBusinessObject formBusinessObject)
    {
        //return await _httpClient.PostAsJsonAsync<string>($"api/StartChatsForm/Save", formBusinessObject);
        var userId = await localStorageService.GetValue(ClaimTypes.NameIdentifier);

        ArgumentException.ThrowIfNullOrEmpty(userId);
        ArgumentException.ThrowIfNullOrEmpty(formBusinessObject.FriendId);

        var participant = new string[]
       {
             userId.ToUpper(),
            formBusinessObject.FriendId.ToUpper()
       };

        var conversationId = CombineGuidsXor(participant.OrderBy(x => x).First(), participant.OrderBy(x => x).Last());
        var conversation = await context.Conversations
                .Where(c => c.Id == conversationId)
                .FirstOrDefaultAsync();


        if (conversation == null)
        {
            conversation = new DeepMessage.Client.Common.Data.Conversation
            {
                Id = conversationId,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false,
                Type = ConversationType.Direct,
                Title = "Direct Chat"

            };
            context.Conversations.Add(conversation);

            var participants = new List<ConversationParticipant>
                        {
                            new ConversationParticipant
                            {
                                Id = Guid.CreateVersion7().ToString(),
                                UserId = userId!,
                                 ConversationId = conversation.Id,
                                IsAdmin = true,
                                JoinedAt = DateTime.UtcNow
                            },
                            new ConversationParticipant
                            {
                                Id = Guid.CreateVersion7().ToString(),
                                UserId = formBusinessObject.FriendId!,
                                ConversationId = conversation.Id,
                                IsAdmin = false,
                                JoinedAt = DateTime.UtcNow
                            }
                        };
            context.ConversationParticipants.AddRange(participants);
            await context.SaveChangesAsync();
            chatSyncUpService.Sync(new ChatSyncItem() { Id = conversation.Id, SyncType = 0 });
        }
        return conversation.Id;

    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public Task<StartChatFormBusinessObject> GetItemByIdAsync(string id)
    {
        return Task.FromResult(new StartChatFormBusinessObject());
    }
}
