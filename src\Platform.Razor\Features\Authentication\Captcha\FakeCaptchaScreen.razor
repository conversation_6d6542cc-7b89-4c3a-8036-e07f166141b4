@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.Account
@using ModelFury.Briefly.MobileApp.Features.Account
@using Platform.Framework.Core
@page "/captcha"
@page "/verify"
@inherits FormBase<SigninFormBusinessObject, SignInFormViewModel, string, ISignInFormDataService>

<!-- Main Container with Nothing Phone Theme -->
<div class="p-4 w-full max-w-sm mx-auto bg-white dark:bg-gray-900 rounded-lg shadow-lg">
    @if (string.IsNullOrEmpty(NickName))
    {
        <div class="mb-4">
            <div class="border border-gray-300 dark:border-gray-600 rounded p-2 bg-gray-50 dark:bg-gray-800">
               Activate Application using Purchase or Referral code
            </div>
        </div>
    }
    else
    {
        <!-- App Activation Status -->
        <div class="mb-3">
            <div class="text-xs text-gray-600 dark:text-gray-400 text-center">
                App is activated for <span class="font-mono text-gray-800 dark:text-gray-200">@ObfuscateUsername(NickName)</span>
            </div>
        </div>

        <!-- Captcha Display -->
        <div class="mb-4">
            <div class="border border-gray-300 dark:border-gray-600 rounded p-2 bg-gray-50 dark:bg-gray-800">
                <img src="images/c1.jpg" class="w-full h-auto rounded" />
                <div class="mt-2 flex justify-between text-xs text-blue-600 dark:text-blue-400">
                    <button type="button">↻ Refresh</button>
                    <button type="button">🔊 Audio</button>
                </div>
            </div>
        </div>

        <!-- Action Links -->
        <div class="mb-4 text-center">
            <button type="button"
                    @onclick="ResetRegistration"
                    class="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 underline transition-colors">
                Reset registration
            </button>
        </div>
    }

    <EditForm Model="SelectedItem" OnValidSubmit="HandleFormSubmit">
        <DataAnnotationsValidator /> 

        <!-- Verification Field -->
        <div class="mb-4">
            <label for="authCode" class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                @(string.IsNullOrEmpty(NickName) ? "Activation Code" : "Verification Code")
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2..." />
                    </svg>
                </div>
                <InputText @bind-Value="SelectedItem!.PassKey"
                           id="authCode"
                           type="password"
                           placeholder="Code"
                           class="block w-full pl-9 pr-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 focus:ring-blue-500 focus:border-blue-500" />
            </div>
            <ValidationMessage For="@(() => SelectedItem!.PassKey)" class="text-xs text-red-600 dark:text-red-400 mt-1" />
        </div>

        <!-- Submit Button -->
        <button type="submit"
                disabled="@(IsWorking)"
                class="w-full py-2 text-sm font-medium rounded bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
            @if (IsWorking)
            {
                <i class="fa-regular fa-sync fa-spin mr-1"></i>
                <span>Verifying...</span>
                        }
            else
            {
                <i class="fa-regular fa-lock mr-1"></i>
                <span>Verify</span>
                        }
        </button>

        <!-- Error -->
        @if (!string.IsNullOrEmpty(Error))
        {
            <div class="mt-3 text-sm text-red-700 dark:text-red-300">
                <p>@Error</p>
            </div>
        }
    </EditForm>
</div>
