using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using System.Text.Json;
using DeepMessage.ServiceContracts.Features.Account;
using ModelFury.Briefly.MobileApp.Features.Account;
using Microsoft.JSInterop;
using Platform.Razor.Features.Authentication.SignIn;
using Platform.Framework.Core;
using Microsoft.Extensions.Logging;
using DeepMessage.Client.Common.Data;
using Microsoft.Extensions.DependencyInjection;
using System.Security.Claims;

namespace Platform.Razor.Features.Authentication.SignUp
{
    public partial class SignUpForm
    {
        [Parameter, SupplyParameterFromQuery]
        public string? Code { get; set; }
         

        protected override async Task<SignupFormViewModel> CreateSelectedItem()
        {
            return await Task.FromResult(new SignupFormViewModel
            {
                NickName = string.Empty,
                PassKey = string.Empty,
                DeviceString = await GetDeviceString(),
                ReferralCode = Code
            });
        }

        /// <summary>
        /// Gets device information string
        /// </summary>
        private async Task<string> GetDeviceString()
        {
            try
            {
                var userAgent = await JsRuntime.InvokeAsync<string>("eval", "navigator.userAgent");
                var platform = await JsRuntime.InvokeAsync<string>("eval", "navigator.platform");
                return $"Web-{platform}-{userAgent.Split(' ').LastOrDefault()}";
            }
            catch
            {
                return "Web-Unknown-Browser";
            }
        }
         

        /// <summary>
        /// Handles successful registration
        /// </summary>
        public override async Task OnAfterSaveAsync(string authResult)
        {
            try
            {
                // Parse the authentication result
                var authClaims = JsonSerializer.Deserialize<AuthorizationClaimsModel>(authResult);

                if (authClaims != null)
                {
                    // Store authentication tokens
                    await StoreAuthenticationTokens(authClaims);
                     
                    // Notify authentication state provider
                    if (AuthStateProvider is IAuthenticationStateNotifier notifier)
                    {
                        await notifier.NotifyAuthenticationStateChanged();
                    }
                     
                    Navigation.NavigateTo("/chat", replace: true);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing registration result");
                Error = "Registration succeeded but there was an error processing the result. Please try signing in.";
            }
        }

        /// <summary>
        /// Stores authentication tokens
        /// </summary>
        private async Task StoreAuthenticationTokens(AuthorizationClaimsModel authClaims)
        {
            try
            {
                var scope = ScopeFactory.CreateScope();
                var localStorage = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
                await localStorage.SetValue(authClaims.Token, "auth_token");
                await localStorage.SetValue(authClaims.UserId, ClaimTypes.NameIdentifier);
                await localStorage.SetValue(authClaims.Username, ClaimTypes.Name);

                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                var user = await context.ApplicationUsers.FindAsync(authClaims.UserId);
                if (user == null)
                {
                    user = new ApplicationUser()
                    {
                        Id = authClaims.UserId,
                        NickName = authClaims.Username,
                        Hash = "dummy",
                    };
                    context.ApplicationUsers.Add(user);
                    await context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error storing authentication tokens");
            }
        }
 
    }
}
