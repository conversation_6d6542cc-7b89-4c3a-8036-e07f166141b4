﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Account;
namespace ModelFury.Briefly.MobileApp.Features.Account;
public class SignInClientSideFormDataService : ISignInFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public SignInClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(SigninFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/SignInForm/Save", formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<SigninFormBusinessObject> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<SigninFormBusinessObject>($"api/SignInForm/GetItemById?id=" + id);
	}
}
