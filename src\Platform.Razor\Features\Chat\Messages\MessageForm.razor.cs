using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.JSInterop;
using Platform.Client.Services.Features.Conversation;
using Platform.Client.Services.Services;
using System.Timers;

namespace Platform.Razor.Features.Chat.Messages
{
    public partial class MessageForm
    {
        [Parameter] public string ConversationId { get; set; } = string.Empty;
        [Parameter] public EventCallback OnMessageSent { get; set; }
     
        private ElementReference messageInput;
         
        private List<IBrowserFile>? selectedFiles;

        [Inject]
        IClientEncryptionService _encryptionService { get; set; } = default!;

        [Inject]
        ISecureKeyManager _secureKeyManager { get; set; } = default!;

        protected override void OnInitialized()
        {
            if (SelectedItem != null)
            {
                SelectedItem.ConversationId = ConversationId;
            }
            base.OnInitialized();
        }

        protected override async Task<ChatMessageFormViewModel> CreateSelectedItem()
        {
            return await Task.FromResult(new ChatMessageFormViewModel
            {
                ConversationId = ConversationId,
                Content = string.Empty,
                ContentType = 0 // Text message
            });
        }

        protected override ChatMessageFormBusinessObject ConvertViewModelToBusinessModel(ChatMessageFormViewModel formViewModel)
        {
            return new ChatMessageFormBusinessObject
            {
                ConversationId = formViewModel.ConversationId,
                Content = formViewModel.Content?.Trim(),
                ContentType = formViewModel.ContentType
            };
        }

        /// <summary>
        /// Sends the message
        /// </summary>
        private async Task SendMessage()
        {
            if (string.IsNullOrWhiteSpace(SelectedItem?.Content) || IsWorking)
                return;

            try
            {
                await HandleFormSubmit();
                
                // Clear the input after successful send
                if (SelectedItem != null)
                {
                    SelectedItem.Content = string.Empty;
                }
                
                // Clear files if any
                selectedFiles?.Clear();
                 
                // Focus back to input
                await JsRuntime.InvokeVoidAsync("focusElement", messageInput);
                
                // Notify parent component
                await OnMessageSent.InvokeAsync();
            }
            catch (Exception ex)
            {
                Error = ex.Message;
            }
        }  

        /// <summary>
        /// Triggers the file upload dialog
        /// </summary>
        private async Task TriggerFileUpload()
        {
            //if (fileInput != null)
            //{
            //    await JSRuntime.InvokeVoidAsync("triggerFileInput", fileInput.Element);
            //}
        }

        /// <summary>
        /// Handles file selection
        /// </summary>
        private async Task HandleFileSelected(InputFileChangeEventArgs e)
        {
            selectedFiles = e.GetMultipleFiles(10).ToList(); // Limit to 10 files
            
            // TODO: Implement file upload logic
            // For now, just show the selected files
            StateHasChanged();
        }

        /// <summary>
        /// Removes a selected file
        /// </summary>
        private void RemoveFile(IBrowserFile file)
        {
            selectedFiles?.Remove(file);
            StateHasChanged();
        }

        /// <summary>
        /// Clears all selected files
        /// </summary>
        private void ClearFiles()
        {
            selectedFiles?.Clear();
            StateHasChanged();
        }

        /// <summary>
        /// Formats file size for display
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        public override async Task OnAfterSaveAsync(string key)
        {
            // Message sent successfully
            await base.OnAfterSaveAsync(key);
        }

     

        //protected override void Dispose()
        //{
        //    _typingTimer?.Stop();
        //    _typingTimer?.Dispose();
        //    base.Dispose();
        //}
    }
}
