﻿using DeepMessage.Framework.Core;
using System.ComponentModel.DataAnnotations;
namespace ModelFury.Briefly.MobileApp.Features.Account;
public class SigninFormViewModel : ObservableBase, IValidateable
{ 
    public string? NickName { get; set; }

    [Required(ErrorMessage = "Code is required")]
    [MinLength(6, ErrorMessage = "Enter a Valid Code")]
    private string? _passKey;

    public string? PassKey
    {
        get { return _passKey; }
        set
        {
            _passKey = value;
            SetField(ref _passKey, value);
            NotifyPropertyChanged(nameof(IsPassword));
            NotifyPropertyChanged(nameof(SearchIcon)); 
        }
    }
     
    public string? DeviceString { get; set; }
 
    private bool _showPassword;
    public bool ShowPassword
    {
        get { return _showPassword; }
        set { SetField(ref _showPassword, value); }
    }


    public bool IsPassword
    {
        get { return PassKey != null && (PassKey.StartsWith("***") || PassKey.StartsWith("###"));  }
    }
     
    public string SearchIcon
    {
        get { return PassKey != null && (PassKey.StartsWith("***") || PassKey.StartsWith("###")) ? "arrow_right_to_arc_light.svg" : "search.svg"; }
    }

    public string NickNameHint
    {
        get { return string.IsNullOrEmpty(NickName) ? "This device is not registered" : $"Registered - ******{NickName.Last()}"; }
    }

    public void Validate()
    {
        if (string.IsNullOrEmpty(PassKey))
        {
            throw new ValidationException("Password is required");
        } 
    }
}
