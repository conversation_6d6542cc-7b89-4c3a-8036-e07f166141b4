﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
namespace DeepMessage.Server.DataServices.Features.Conversation;
public class ChatMessageServerSideFormDataService : IChatMessageFormDataService
{

    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public ChatMessageServerSideFormDataService(AppDbContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(ChatMessageFormBusinessObject formBusinessObject)
    {
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        ArgumentException.ThrowIfNullOrEmpty(formBusinessObject.ConversationId);
        ArgumentException.ThrowIfNullOrEmpty(userId);

        // Input validation
        if (string.IsNullOrWhiteSpace(formBusinessObject.Content))
        {
            throw new ArgumentException("Message content cannot be empty");
        }

        if (formBusinessObject.Content.Length > 4000) // Reasonable message length limit
        {
            throw new ArgumentException("Message content exceeds maximum length of 4000 characters");
        }

        // Sanitize content (basic HTML encoding to prevent XSS)
        var sanitizedContent = System.Net.WebUtility.HtmlEncode(formBusinessObject.Content.Trim());

        // Verify user is participant in the conversation
        var isParticipant = await _context.ConversationParticipants
            .AnyAsync(cp => cp.ConversationId == formBusinessObject.ConversationId && cp.UserId == userId);

        if (!isParticipant)
        {
            throw new UnauthorizedAccessException("User is not a participant in this conversation");
        }

        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var message = new Message()
            {
                Id = Guid.CreateVersion7().ToString(),
                ConversationId = formBusinessObject.ConversationId,
                CreatedAt = DateTime.UtcNow,
                PlainContent = sanitizedContent,
                SenderId = userId,
                DeliveryStatus = ServiceContracts.Enums.DeliveryStatus.Pending
            };

            _context.Messages.Add(message);
            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            return message.Id;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public Task<ChatMessageFormBusinessObject> GetItemByIdAsync(string id)
    {
        throw new NotImplementedException();
    }
}
