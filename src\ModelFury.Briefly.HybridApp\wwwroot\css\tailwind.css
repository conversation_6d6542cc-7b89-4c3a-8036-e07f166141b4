﻿@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --rz-input-height: 2.625rem;
}
/*** ARM Start ***/
@layer components {
    /* Page Transition Effects */
    .page-transition {
        @apply transition-all duration-300 ease-in-out;
    }

    /* WhatsApp-inspired page transitions */
    .page-fade-in {
        animation: fadeInSlide 300ms ease-out forwards;
    }

    .page-fade-out {
        animation: fadeOutSlide 300ms ease-in forwards;
    }

    .tab-transition {
        @apply transition-all duration-200 ease-in-out;
    }

    /* Chat to Messages Transition - Right to Left */
    .chat-slide-in {
        animation: slideInFromRight 400ms ease-out forwards;
    }

    .chat-slide-out {
        animation: slideOutToLeft 400ms ease-in forwards;
    }

    /* WhatsApp-style message animations */
    .message-slide-in {
        animation: messageSlideIn 200ms ease-out forwards;
    }

    .message-slide-out {
        animation: messageSlideOut 200ms ease-in forwards;
    }

    /* Loading spinner for Nothing Phone style */
    .spinner-nothing {
        @apply animate-spin rounded-full border-2 border-primary-500 border-t-transparent;
    }

    /* Smooth hover transitions for rows */
    .row-hover {
        @apply transition-colors duration-150 ease-in-out;
    }

    /* Button press animation */
    .btn-press {
        @apply transition-transform duration-75 ease-in-out active:scale-95;
    }

    /* Nothing Phone Black/Red Button Styles */
    .btn-primary {
        @apply border-nothing-black-800 bg-nothing-black-800 flex items-center justify-center gap-2 rounded-lg border px-3 py-2 text-center text-sm font-medium text-white hover:bg-nothing-black-900 focus:outline-none focus:ring-4 focus:ring-nothing-black-300;
    }

    .btn-primary-light {
        @apply border-nothing-black-100 bg-nothing-black-100 text-nothing-black-800 flex items-center justify-center gap-2 rounded-lg border px-3 py-2 text-center text-sm font-medium hover:bg-nothing-black-200 focus:outline-none focus:ring-4 focus:ring-nothing-black-300;
    }

    .btn-primary-outlined {
        @apply border-nothing-black-800 text-nothing-black-800 flex items-center justify-center gap-2 rounded-lg border bg-transparent px-3 py-2 text-center text-sm font-medium hover:bg-nothing-black-800 hover:text-white focus:outline-none focus:ring-4 focus:ring-nothing-black-300;
    }

    /* Critical Red Button for Important Actions */
    .btn-critical {
        @apply border-nothing-red-500 bg-nothing-red-500 flex items-center justify-center gap-2 rounded-lg border px-3 py-2 text-center text-sm font-medium text-white hover:bg-nothing-red-600 focus:outline-none focus:ring-4 focus:ring-nothing-red-300;
    }

    .btn-critical-outlined {
        @apply border-nothing-red-500 text-nothing-red-500 flex items-center justify-center gap-2 rounded-lg border bg-transparent px-3 py-2 text-center text-sm font-medium hover:bg-nothing-red-500 hover:text-white focus:outline-none focus:ring-4 focus:ring-nothing-red-300;
    }

    .btn-gray-light {
        @apply flex items-center justify-center gap-2 rounded-lg border border-gray-100 bg-gray-100 px-3 py-2 text-center text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-4 focus:ring-gray-300;
    }

    .btn-gray-outlined {
        @apply flex items-center justify-center gap-2 rounded-lg border border-gray-100 bg-transparent px-3 py-2 text-center text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-300;
    }

    .btn-gray-icon {
        @apply flex h-6 w-6 items-center justify-center rounded-lg border border-gray-100 bg-gray-100 text-base font-medium text-gray-600 hover:bg-gray-200 focus:outline-none focus:ring-4 focus:ring-gray-300 disabled:bg-gray-400;
    }

    .btn-primary-icon {
        @apply bg-primary-600 border-primary-600 flex h-6 w-6 items-center justify-center rounded-lg border text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-4 focus:ring-primary-300 disabled:bg-gray-400;
    }

    .btn-danger-icon {
        @apply flex h-6 w-6 items-center justify-center rounded-lg border border-gray-100 bg-gray-100 text-base font-medium text-red-600 hover:border-red-200 hover:bg-red-200 focus:outline-none focus:ring-4 focus:ring-red-300 disabled:bg-red-400;
    }

    .btn-success {
        @apply flex items-center justify-center gap-2 rounded-lg border border-nothing-black-700 bg-nothing-black-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-nothing-black-800 focus:outline-none focus:ring-4 focus:ring-nothing-black-300;
    }

    .btn-success-light {
        @apply flex items-center justify-center gap-2 rounded-lg border border-nothing-black-100 bg-nothing-black-100 px-3 py-2 text-center text-sm font-medium text-nothing-black-700 hover:bg-nothing-black-200 focus:outline-none focus:ring-4 focus:ring-nothing-black-300;
    }

    .btn-success-outlined {
        @apply flex items-center justify-center gap-2 rounded-lg border border-nothing-black-700 bg-transparent px-3 py-2 text-center text-sm font-medium text-nothing-black-700 hover:bg-nothing-black-700 hover:text-white focus:outline-none focus:ring-4 focus:ring-nothing-black-300;
    }

    .btn-xs {
        @apply text-xs;
    }

    .btn-sm {
        @apply py-2;
    }

    .btn-base {
        @apply px-5 py-2.5 text-sm;
    }

    .btn-lg {
        @apply px-5 py-3 text-base;
    }

    .btn-xl {
        @apply px-6 py-3.5 text-base;
    }

    .badge {
        @apply flex items-center justify-center rounded-md px-2.5 py-1 align-middle text-xs font-medium;
    }

    .badge-pink {
        @apply bg-pink-100 text-pink-800;
    }

    .badge-primary {
        @apply bg-primary-100 text-primary-800;
    }

    .badge-green {
        @apply bg-green-100 text-green-800;
    }

    .badge-red {
        @apply bg-red-100 text-red-800;
    }

    .badge-yellow {
        @apply bg-yellow-100 text-yellow-800;
    }

    .badge-gray {
        @apply bg-gray-100 text-gray-800;
    }

    .notification-badge-primary {
        @apply bg-primary-600 shrink-0 flex h-5 w-5 items-center justify-center rounded-full text-xs text-white;
    }

    .notification-badge-red {
        @apply shrink-0 flex h-5 w-5 items-center justify-center rounded-full bg-red-600 text-xs text-white;
    }

    .pulse-red {
        animation: pulse 2s infinite;
        -webkit-animation-name: pulse-red;
        animation-name: pulse-red;
    }

    .label-dark {
        @apply text-sm font-medium text-gray-900;
    }

    .label-light {
        @apply text-sm font-medium text-gray-500;
    }

    .form-control {
        @apply block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 disabled:bg-gray-200 disabled:text-gray-400;
    }

    .form-select, .rz-dropdown {
        @apply block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 hover:!border-gray-300 focus:!outline focus:!outline-1 focus:!outline-primary-600 focus:!shadow-none focus:!border-primary-600 focus:!ring-primary-600;
    }

    .form-check {
        @apply text-primary-600 h-4 w-4 rounded border-gray-300 bg-gray-100 focus:ring-2 focus:ring-primary-600;
    }

    input[type="radio"].form-check {
        @apply rounded-full;
    }

    .form-switch {
        @apply relative flex cursor-pointer items-center gap-2;
        .switch-style

{
    @apply h-5 w-9 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-4 after:w-4 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 peer-checked:after:translate-x-full peer-checked:after:border-white
}

}

.form-group {
    @apply flex w-full flex-col gap-2;
}

.breadcrumb {
    @apply flex items-center text-sm font-medium;
    .breadcrumb-item

{
    @apply flex items-center;
    a

{
    @apply flex items-center text-gray-700 hover:text-primary-600;
}

i {
    @apply text-sm rtl:rotate-180;
}

}

li:not(:first-child)::before {
    @apply content-["\f054"] font-fa mx-2 font-semibold text-gray-400 rtl:rotate-180;
}

.active span {
    @apply text-gray-500;
}

}

div.breadcrumb-item {
    a, span

{
    @apply ms-1 md:ms-2;
}

}

/*.active span {
    @apply text-gray-500;
}*/

.pagination-container {
    @apply grid-cols-8 mx-auto grid w-fit items-center justify-center gap-2 px-2 py-2 md:flex md:px-4 md:gap-3 lg:flex-row;
    select

{
    @apply col-span-2 order-3 block w-auto rounded-lg border border-gray-300 bg-gray-50 px-3 py-1.5 pr-7 text-sm text-gray-900 focus:ring-primary-600 focus:border-primary-600 md:order-3;
}

.results {
    @apply order-2 col-span-6 text-sm font-normal text-gray-500 md:order-1;
}

}

.pagination {
    @apply order-1 col-span-8 mx-auto flex items-center -space-x-px md:order-2;
    .previous

{
    @apply border-e-0 ms-0 flex h-[34px] items-center justify-center rounded-s-lg border border-gray-300 bg-white px-3 leading-tight text-gray-500 hover:bg-gray-100 hover:text-gray-700;
}

.next {
    @apply flex h-[34px] items-center justify-center rounded-e-lg border border-gray-300 bg-white px-3 leading-tight text-gray-500 hover:bg-gray-100 hover:text-gray-700;
}

.page-link {
    @apply flex h-[34px] items-center justify-center border border-gray-300 bg-white px-3 leading-tight text-gray-500 hover:bg-gray-100 hover:text-gray-700;
    &.active

{
    @apply z-10 !text-primary-600 !border-primary-300 !bg-primary-50 hover:!bg-primary-100 hover:!text-primary-600;
}

}
}

.invalid-feedback {
    @apply text-sm text-red-600;
}

.chat-card {
    @apply flex w-full items-center gap-3 px-5 py-2 text-left hover:bg-primary-50 focus:outline-none focus-visible:bg-indigo-50;
    &.unread, &.active

{
    @apply bg-primary-100/50;
}

.chat-head {
    @apply flex items-center justify-between;
    h3

{
    @apply line-clamp-1 text-sm font-semibold text-gray-900;
}

.location {
    @apply shrink-0 flex items-center gap-2 text-xs;
    img

{
    @apply h-4 w-4;
}

}

time {
    @apply text-2xs;
}

}

.chat-content {
    @apply flex items-center justify-between;
    .inquiry

{
    @apply line-clamp-1 text-sm;
}

}

.msg {
    @apply flex items-center gap-1 text-xs;
    i

{
    @apply text-gray-400;
}

}
}


.alert {
    @apply mb-4 flex items-center gap-2 rounded-lg border p-4 text-sm;
}

.alert-danger {
    @apply border-red-300 bg-red-50 text-red-800;
}

.alert-success {
    @apply border-green-300 bg-green-50 text-green-800;
}

.alert-info {
    @apply border-blue-300 bg-blue-50 text-blue-800;
}

.aside-nav {
    @apply -translate-x-full left-0 top-0 z-50 overflow-y-auto bg-white transition-transform lg:h-full lg:w-[5.6rem];
    &.open

{
    @apply drop-shadow-right fixed h-full w-full px-3 md:px-0 lg:drop-shadow lg:relative lg:w-[5.6rem];
}

&.close {
    @apply drop-shadow;
}

.nav {
    @apply flex flex-col gap-1 overflow-y-auto lg:gap-2 lg:px-2;
    .nav-link

{
    @apply flex items-center gap-1.5 rounded-lg p-3 text-left text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-700 lg:flex-col lg:text-center lg:pt-0.5 lg:pb-1 lg:px-1 lg:justify-center lg:gap-0 lg:text-2xs;
    &.active

{
    @apply text-primary-700 bg-primary-100;
}

i {
    @apply min-h-[21px] w-8 text-center text-xl md:w-auto;
}

}
}
}
}

.alert-banner {
    @apply z-[32] relative left-0 right-0 m-auto flex w-full items-center justify-center gap-2 px-2.5 py-2 text-center font-medium text-white lg:absolute lg:w-fit lg:rounded-lg lg:top-0.5 lg:h-11;
    p

{
    @apply flex items-center gap-2 text-center text-xs;
}

}
/*** ARM End ***/
/* @layer base{
  @font-face{
    font-family:"jameel-noori-nastaleeq";
    src:url("../fonts/jameel-noori-nastaleeq.ttf") format("woff"),
    url("../fonts/jameel-noori-nastaleeq.ttf") format("opentype"),
    url("../fonts/jameel-noori-nastaleeq.ttf") format("truetype");
  }
} */
[x-cloak] {
    display: none !important;
}

/*[x-disclosure\:panel]:has(li.select2-selection__choice) {
    @apply !block;
}*/

@media (min-width: 1024px) {
    /* Hide scrollbar for Chrome, Safari and Opera */
    ::-webkit-scrollbar-track {
        background-color: transparent;
    }

        ::-webkit-scrollbar-track:hover {
            @apply bg-gray-300;
        }

    ::-webkit-scrollbar-thumb {
        @apply bg-primary-400 rounded-full;
    }

        ::-webkit-scrollbar-thumb:hover {
            @apply bg-primary-600;
        }

    ::-webkit-scrollbar {
        width: 7px;
        height: 4px;
    }

    .scroll {
        @apply overflow-y-auto;
    }

        .scroll:active::-webkit-scrollbar-thumb,
        .scroll:focus::-webkit-scrollbar-thumb,
        .scroll:hover::-webkit-scrollbar-thumb {
            @apply visible;
        }

        .scroll::-webkit-scrollbar-thumb {
            @apply bg-primary-400 invisible rounded-full;
        }

            .scroll::-webkit-scrollbar-thumb:hover {
                @apply bg-primary-600;
            }
}

.city-name::before {
    content: "";
    height: 50%;
    background: linear-gradient(0deg, #000000 0%, rgba(0, 0, 0, 0) 50.17%);
    z-index: 0;
    @apply absolute bottom-0 w-full rounded-lg md:rounded-xl;
}

.swiper-button-disabled {
    opacity: .35;
    cursor: auto;
    pointer-events: none;
}

.swiper-pagination-bullet {
    width: var(--swiper-pagination-bullet-width,var(--swiper-pagination-bullet-size,16px)) !important;
    height: var(--swiper-pagination-bullet-height,var(--swiper-pagination-bullet-size,16px)) !important;
}

.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet, .swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 0 var(--swiper-pagination-bullet-horizontal-gap,6px) !important;
}

/* <link rel="stylesheet" href="https://unpkg.com/tippy.js@6/dist/tippy.css" /> */
.tippy-box[data-animation=fade][data-state=hidden] {
    opacity: 0
}

[data-tippy-root] {
    max-width: calc(100vw - 10px)
}

.tippy-box {
    position: relative;
    background-color: #333;
    color: #fff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
    white-space: normal;
    outline: 0;
    transition-property: transform,visibility,opacity
}

    .tippy-box[data-placement^=top] > .tippy-arrow {
        bottom: 0
    }

        .tippy-box[data-placement^=top] > .tippy-arrow:before {
            bottom: -7px;
            left: 0;
            border-width: 8px 8px 0;
            border-top-color: initial;
            transform-origin: center top
        }

    .tippy-box[data-placement^=bottom] > .tippy-arrow {
        top: 0
    }

        .tippy-box[data-placement^=bottom] > .tippy-arrow:before {
            top: -7px;
            left: 0;
            border-width: 0 8px 8px;
            border-bottom-color: initial;
            transform-origin: center bottom
        }

    .tippy-box[data-placement^=left] > .tippy-arrow {
        right: 0
    }

        .tippy-box[data-placement^=left] > .tippy-arrow:before {
            border-width: 8px 0 8px 8px;
            border-left-color: initial;
            right: -7px;
            transform-origin: center left
        }

    .tippy-box[data-placement^=right] > .tippy-arrow {
        left: 0
    }

        .tippy-box[data-placement^=right] > .tippy-arrow:before {
            left: -7px;
            border-width: 8px 8px 8px 0;
            border-right-color: initial;
            transform-origin: center right
        }

    .tippy-box[data-inertia][data-state=visible] {
        transition-timing-function: cubic-bezier(.54,1.5,.38,1.11)
    }

.tippy-arrow {
    width: 16px;
    height: 16px;
    color: #333
}

    .tippy-arrow:before {
        content: "";
        position: absolute;
        border-color: transparent;
        border-style: solid
    }

.tippy-content {
    position: relative;
    padding: 5px 9px;
    z-index: 1
}

/* ::-webkit-scrollbar:hover{
  width: 4px;
} */
/* .scrollbar-hide {
	scrollbar-width: auto;
} */
/*.scrollbar-hide::-webkit-scrollbar {
   display: none; 
} */
/* Hide scrollbar for IE, Edge and Firefox */
/*.scrollbar-hide {*/
/* -ms-overflow-style: none;   */
/* IE and Edge */
/* scrollbar-width: none;   */
/* Firefox */
/*}*/



/*** Select 2 ***/

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 24px;
}

.select2-selection.select2-selection--single {
    height: 46px;
}

.select2-results__options[aria-multiselectable="true"] .select2-results__option, .select2-results__options .select2-results__option {
    padding-right: 20px;
    vertical-align: middle;
}

    .select2-results__options[aria-multiselectable="true"] .select2-results__option:before, .select2-results__options .select2-results__option:before {
        content: "";
        display: inline-block;
        position: relative;
        height: 20px;
        width: 20px;
        border: 2px solid #e9e9e9;
        border-radius: 4px;
        background-color: #fff;
        margin-right: 20px;
        vertical-align: middle;
    }

    .select2-results__options[aria-multiselectable="true"] .select2-results__option.select2-results__option--selected:before, .select2-results__options .select2-results__option.select2-results__option--selected:before {
        content: "\2713";
        color: #fff;
        background-color: #673AB7;
        border: 0;
        display: inline-block;
        padding-left: 5px;
        font-size: 15px;
    }

.select2-results__options[aria-multiselectable="true"] .select2-results__message.select2-results__option:before, .select2-results__options .select2-results__message.select2-results__option:before {
    content: "";
    margin-right: 0;
    height: 0;
    width: 0;
}


.select2-selection.select2-selection--multiple, .select2-selection.select2-selection--single {
    @apply block w-full !rounded-lg !border !border-solid !border-gray-300 !bg-gray-50 px-4 !py-3 text-xs !font-medium font-normal text-gray-500;
}

.select2-container--default.select2-container--focus .select2-selection--multiple, .select2-container--default.select2-container--focus .select2-selection--single {
    @apply !border-primary-600 outline-2 !border outline-red-500;
}

.select2-container--default .select2-search--inline .select2-search__field {
    @apply !mt-0 h-5;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice, .select2-container--default .select2-selection--single .select2-selection__choice {
    @apply bg-primary-100 text-primary-700 border-0 py-0.5 pl-1 pr-4;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove, .select2-container--default .select2-selection--single .select2-selection__choice__remove {
    @apply text-primary-600 border-r-0 left-[auto] right-0 -mt-1 pl-1 pr-1 text-lg;
}

.select2-results__options[aria-multiselectable=true] .select2-results__option, .select2-results__options .select2-results__option {
    @apply text-xs;
}

.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
    @apply bg-primary-600 text-xs;
}

.select2-results__options[aria-multiselectable=true] .select2-results__option.select2-results__option--selected:before, .select2-results__options .select2-results__option.select2-results__option--selected:before {
    @apply pt-[3px];
}


[popover]:popover-open {
    position: absolute;
    inset: unset;
    left: 24px;
    margin: 0;
}

/*[popover]:popover-open button{
    @apply md:hidden; 
}*/

[popover].info::before {
    content: '💡 ';
}

[popover].success::before {
    content: '✅ ';
}

[popover].warning::before {
    content: '⚠️ ';
}

[popover].danger::before {
    content: '🚨 ';
}

.judgement-detail a {
    @apply text-primary-600 font-medium;
}



/*** Congratulations animation css ***/

@keyframes confetti-slow {
    0% {
        transform: translate3d(0, 0, 0) rotateX(0) rotateY(0);
    }

    100% {
        transform: translate3d(25px, 105vh, 0) rotateX(360deg) rotateY(180deg);
    }
}

@keyframes confetti-medium {
    0% {
        transform: translate3d(0, 0, 0) rotateX(0) rotateY(0);
    }

    100% {
        transform: translate3d(100px, 105vh, 0) rotateX(100deg) rotateY(360deg);
    }
}

@keyframes confetti-fast {
    0% {
        transform: translate3d(0, 0, 0) rotateX(0) rotateY(0);
    }

    100% {
        transform: translate3d(-50px, 105vh, 0) rotateX(10deg) rotateY(250deg);
    }
}

.confetti-container {
    perspective: 700px;
    position: absolute;
    overflow: hidden;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.confetti {
    position: absolute;
    z-index: 1;
    top: -10px;
    border-radius: 0%;
}

.confetti--animation-slow {
    animation: confetti-slow 2.25s linear 1 forwards;
}

.confetti--animation-medium {
    animation: confetti-medium 1.75s linear 1 forwards;
}

.confetti--animation-fast {
    animation: confetti-fast 1.25s linear 1 forwards;
}

/* Checkmark */
.checkmark-circle {
    width: 150px;
    height: 150px;
    position: relative;
    display: inline-block;
    vertical-align: top;
    margin-left: auto;
    margin-right: auto;
}

    .checkmark-circle .background {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background: #00C09D;
        position: absolute;
    }

    .checkmark-circle .checkmark {
        border-radius: 5px;
    }

        .checkmark-circle .checkmark.draw:after {
            -webkit-animation-delay: 100ms;
            -moz-animation-delay: 100ms;
            animation-delay: 100ms;
            -webkit-animation-duration: 3s;
            -moz-animation-duration: 3s;
            animation-duration: 3s;
            -webkit-animation-timing-function: ease;
            -moz-animation-timing-function: ease;
            animation-timing-function: ease;
            -webkit-animation-name: checkmark;
            -moz-animation-name: checkmark;
            animation-name: checkmark;
            -webkit-transform: scaleX(-1) rotate(135deg);
            -moz-transform: scaleX(-1) rotate(135deg);
            -ms-transform: scaleX(-1) rotate(135deg);
            -o-transform: scaleX(-1) rotate(135deg);
            transform: scaleX(-1) rotate(135deg);
            -webkit-animation-fill-mode: forwards;
            -moz-animation-fill-mode: forwards;
            animation-fill-mode: forwards;
        }

        .checkmark-circle .checkmark:after {
            opacity: 1;
            height: 75px;
            width: 37.5px;
            -webkit-transform-origin: left top;
            -moz-transform-origin: left top;
            -ms-transform-origin: left top;
            -o-transform-origin: left top;
            transform-origin: left top;
            border-right: 15px solid white;
            border-top: 15px solid white;
            border-radius: 2.5px !important;
            content: "";
            left: 25px;
            top: 75px;
            position: absolute;
        }

@-webkit-keyframes checkmark {
    0% {
        height: 0;
        width: 0;
        opacity: 1;
    }

    20% {
        height: 0;
        width: 37.5px;
        opacity: 1;
    }

    40% {
        height: 75px;
        width: 37.5px;
        opacity: 1;
    }

    100% {
        height: 75px;
        width: 37.5px;
        opacity: 1;
    }
}

@-moz-keyframes checkmark {
    0% {
        height: 0;
        width: 0;
        opacity: 1;
    }

    20% {
        height: 0;
        width: 37.5px;
        opacity: 1;
    }

    40% {
        height: 75px;
        width: 37.5px;
        opacity: 1;
    }

    100% {
        height: 75px;
        width: 37.5px;
        opacity: 1;
    }
}

@keyframes checkmark {
    0% {
        height: 0;
        width: 0;
        opacity: 1;
    }

    20% {
        height: 0;
        width: 37.5px;
        opacity: 1;
    }

    40% {
        height: 75px;
        width: 37.5px;
        opacity: 1;
    }

    100% {
        height: 75px;
        width: 37.5px;
        opacity: 1;
    }
}

/*** Dialog Height ***/
dialog::backdrop {
    @apply bg-blue-950/60;
}

@media (min-height: 500px) and (min-width: 500px) {
    .wizard-dialog {
        @apply h-[calc(100dvh-8rem)];
    }
}

@media (min-height: 900px) {
    .wizard-dialog {
        @apply h-[calc(100dvh-21rem)];
    }
}

/*** subscription alert ***/
body:has(.nosubscription) .ktcontainer {
    @apply md:!h-[calc(100vh-5.5rem)];
}

@media (max-width: 768px) {
    body:has(.nosubscription + header.hidden) .ktcontainer {
        @apply h-[calc(100dvh-5rem)];
    }
}


.loading-progress {
    position: relative;
    display: block;
    width: 8rem;
    height: 8rem;
    margin: 20vh auto 1rem auto;
}

    .loading-progress circle {
        fill: none;
        stroke: #e0e0e0;
        stroke-width: 0.6rem;
        transform-origin: 50% 50%;
        transform: rotate(-90deg);
    }

        .loading-progress circle:last-child {
            stroke: #5152b1;
            stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
            transition: stroke-dasharray 0.05s ease-in-out;
        }

.loading-progress-text {
    position: absolute;
    text-align: center;
    font-weight: bold;
    inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
    z-index: -1;
}

    .loading-progress-text:after {
        content: var(--blazor-load-percentage-text, "Loading");
    }

.kt-radio {
    @apply text-primary-600 mr-2 h-4 w-4 border-gray-300 bg-gray-100 focus:ring-primary-600 focus:ring-1;
}

.kt-label {
    @apply text-sm font-medium text-gray-900;
}


/*** ARM Start ***/
.account-container {
    @apply relative flex grow flex-col overflow-hidden bg-white;
    h1

{
    @apply flex text-2xl font-bold text-white focus:outline-none md:text-4xl;
}

.logo {
    @apply h-12 md:h-16;
}

}

.account-grid {
    @apply grid h-full overflow-visible overflow-x-hidden md:grid-cols-2;
}

.account-left {
    @apply from-primary-600 via-primary-600 relative hidden items-center justify-center bg-gradient-to-r to-pink-500 md:flex;
    section

{
    @apply flex max-w-md flex-col gap-4 p-5 md:p-8 md:max-w-2xl md:gap-8;
}

}

.account-right {
    @apply relative top-0 grid h-full flex-col overflow-visible overflow-y-auto p-5 md:items-center lg:p-7;
    section

{
    @apply mx-auto w-full max-w-sm;
}

h2 {
    @apply text-2xl font-semibold text-gray-900 md:text-3xl;
}

p {
    @apply text-xs font-medium text-gray-900 md:text-base;
}

.text-link {
    @apply text-primary-600 hover:text-primary-700 hover:underline;
}

}

.intro-list {
    @apply flex flex-col space-y-4;
    li

{
    @apply flex items-center gap-2 text-xs font-medium text-white md:text-lg;
}

}

.rz-dropdown-trigger .rzi-chevron-down:before {
    content: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3E%3C/svg%3E");
}

.rz-dropdown-items li:hover, .rz-dropdown-item:hover {
    @apply bg-primary-50 text-primary-600;
}

.rz-dropdown-filter-container .rz-inputtext {
    @apply rounded-lg
}
/*** ARM End ***/
.rz-dropdown {
    @apply form-control;
}

/*** Asad Start ***/
.package-tab {
    @apply flex items-center justify-between gap-1 bg-white px-2 py-4 md:justify-center md:border-b-2 md:px-4;
}

/*** Asad End ***/

.chat-message-received {
    @apply w-fit max-w-[80%] rounded-r-2xl rounded-bl-2xl bg-slate-100 px-2 pb-2 pt-1.5 text-xs font-medium text-gray-900 drop-shadow-sm;
}

.chat-message-sent {
    @apply ml-auto w-fit max-w-[80%] rounded-l-2xl rounded-br-2xl bg-emerald-100 px-2 pb-2 pt-1.5 text-xs font-medium text-gray-900 drop-shadow-sm;
}

.read-more-link {
    @apply text-primary-600 cursor-pointer hover:text-primary-700;
}

.messages time {
    @apply z-10 text-2xs relative float-right -mb-[5px] ml-1 mt-[0.500rem];
}

.chat-bubble {
    @apply bg-primary-600 relative bottom-2 right-2 ml-auto flex h-12 w-12 items-center justify-center rounded-full text-xl text-white hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300;
    .counter

{
    @apply text-3xs absolute -top-1 left-2 h-3 w-3 rounded-full bg-red-600;
}

}

@-webkit-keyframes pulse-red {
    0% {
        -webkit-box-shadow: 0 0 0 rgba(235, 87, 87, 0.6);
    }

    70% {
        -webkit-box-shadow: 0 0 0 10px rgba(235, 87, 87, 0);
    }

    100% {
        -webkit-box-shadow: 0 0 0 0 rgba(235, 87, 87, 0);
    }
}

@keyframes pulse-red {
    0% {
        -moz-box-shadow: 0 0 0 rgba(235, 87, 87, 0.6);
        box-shadow: 0 0 0 rgba(235, 87, 87, 0.6);
    }

    70% {
        -moz-box-shadow: 0 0 0 10px rgba(235, 87, 87, 0);
        box-shadow: 0 0 0 10px rgba(235, 87, 87, 0);
    }

    100% {
        -moz-box-shadow: 0 0 0 0 rgba(235, 87, 87, 0);
        box-shadow: 0 0 0 0 rgba(235, 87, 87, 0);
    }
}


@layer components {
    .results-container {
      @apply mx-auto max-w-6xl bg-gray-50 p-4;
    }
  
    .results-layout {
      @apply flex flex-wrap gap-3;
    }
  
    .results-section {
      @apply rounded-xl bg-white shadow-[0px_1px_2px_0px_rgba(0,0,0,0.08)] px-8 py-4;
      
      &.--main { @apply flex-1; }
      &.--sidebar { @apply w-64; }
    }
  
    .section-heading {
      @apply mb-6 border-b border-gray-200 pb-3 text-center text-xl font-semibold;
    }
  
    .race-container {
      @apply mb-8 flex;
    }
  
    .race-title-block {
      @apply mr-6 flex w-48 items-center justify-center rounded-lg bg-gray-200 p-4 font-medium;
    }
  
    .candidates-grid {
      @apply flex flex-wrap gap-4;
    }
  
    .candidate-card {
      @apply justify-items-center rounded-lg bg-primary-50 p-2 text-center;
      
      &.--winner { @apply mx-auto w-fit; }
    }
  
    .card-image {
      @apply mb-2 h-24 w-24 rounded-lg object-cover;
    }
  
    .card-name {
      @apply text-xs font-bold text-gray-700;
    }
  
    .card-votes {
      @apply text-xs text-primary-500;
    }
  }


.featured-profile-card {
    @apply bg-white rounded-2xl p-3 lg:p-6 shadow-[0px_1px_2px_0px_rgba(0,0,0,0.20)] hover:shadow-xl transition-all duration-200;
}

.card-header {
    @apply flex flex-col items-start gap-3;
}

.icon-wrapper {
    @apply w-12 h-12 flex items-center justify-center shrink-0;
}

.card-content {
    @apply flex-grow;
}

.card-title {
    @apply text-base font-semibold text-gray-900 mb-1;
}

.card-description {
    @apply text-xs text-gray-500;
}

.stats-row {
    @apply grid grid-cols-2 gap-1 mt-3;
}

.stat-badge {
    @apply px-1.5 lg:px-6 py-1 lg:py-2 rounded lg:rounded-lg text-white flex justify-between items-center gap-2;
}

.stat-number {
    @apply text-xs lg:text-xl font-bold;
}

.stat-label {
    @apply text-xs lg:text-base font-medium;
}

.small-cards-grid {
    @apply grid grid-cols-2 md:grid-cols-3 gap-2 lg:gap-6;
}

.small-card {
    @apply featured-profile-card flex flex-col h-full;
}

/* Nothing Phone Black/Red Utilities */
.bg-gradient-nothing {
    background: linear-gradient(135deg, #18181b 0%, #27272a 100%);
}

.bg-gradient-nothing-subtle {
    background: linear-gradient(135deg, #fafafa 0%, #f4f4f5 100%);
}

.bg-gradient-nothing-dark {
    background: linear-gradient(135deg, #09090b 0%, #18181b 100%);
}

.bg-gradient-red-accent {
    background: linear-gradient(135deg, #ff0000 0%, #dc2626 100%);
}

.bg-gradient-red-subtle {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

/* Nothing Phone Text Gradients */
.text-gradient-nothing {
    background: linear-gradient(135deg, #18181b 0%, #27272a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-red {
    background: linear-gradient(135deg, #ff0000 0%, #dc2626 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Nothing Phone Border Gradients */
.border-gradient-nothing {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
               linear-gradient(135deg, #18181b 0%, #27272a 100%) border-box;
}

.border-gradient-red {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
               linear-gradient(135deg, #ff0000 0%, #dc2626 100%) border-box;
}

/* Nothing Phone Loading Spinner */
.spinner-nothing {
    border: 3px solid #f4f4f5;
    border-top: 3px solid #18181b;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-red {
    border: 3px solid #fee2e2;
    border-top: 3px solid #ff0000;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Page Transition Animations */
@keyframes fadeInSlide {
    0% {
        opacity: 0;
        transform: translateX(16px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeOutSlide {
    0% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(-16px);
    }
}

@keyframes slideInFromRight {
    0% {
        opacity: 0;
        transform: translateX(100%);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutToLeft {
    0% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(-100%);
    }
}