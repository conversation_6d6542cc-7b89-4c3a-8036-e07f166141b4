﻿<?xml version="1.0" encoding="utf-8" ?>
<local:SignupFormViewBase
    x:Class="ModelFury.Briefly.MobileApp.Features.Account.SignupFormView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:ModelFury.Briefly.MobileApp.Features.Account"
    Title="Register as new User"
    x:DataType="local:SignupFormView"
    BackgroundColor="#004f98"
    IsBusy="{Binding IsWorking}"
    Shell.BackgroundColor="#004f98"
    Shell.TitleColor="White">
    <Border BackgroundColor="#F4F4F5" StrokeThickness="0">
        <Border.StrokeShape>
            <RoundRectangle CornerRadius="16,16,0,0" />
        </Border.StrokeShape>
        <VerticalStackLayout Padding="16">
            <VerticalStackLayout Margin="0,8" Spacing="10">
                <Label
                    FontAttributes="Bold"
                    FontFamily="MulishExtraBold"
                    FontSize="14"
                    HorizontalTextAlignment="Start"
                    Text="Pick Your Nickname &amp; Passkey"
                    TextColor="#4E4E4E" />
                <Border
                    Background="#EEF3F4"
                    HeightRequest="50"
                    StrokeThickness="0">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="10" />
                    </Border.StrokeShape>
                    <Grid Margin="16,0" ColumnDefinitions="*,Auto">
                        <Entry
                            FontSize="14"
                            Placeholder="Nick Name"
                            PlaceholderColor="#646464"
                            Text="{Binding SelectedItem.NickName}" />
                        <Image Grid.Column="1" Source="lock" />
                    </Grid>
                </Border>
                <Border
                    Background="#EEF3F4"
                    HeightRequest="50"
                    StrokeThickness="0">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="10" />
                    </Border.StrokeShape>
                    <Grid Margin="20,0" ColumnDefinitions="*,Auto">
                        <Entry
                            FontSize="14"
                            HeightRequest="24"
                            IsPassword="True"
                            Placeholder="Pass Key"
                            PlaceholderColor="#646464"
                            Text="{Binding SelectedItem.PassKey}" />
                        <Image Grid.Column="1" Source="lock" />
                    </Grid>
                </Border>
            </VerticalStackLayout>


            <VerticalStackLayout Margin="0,10" Spacing="20">
                <Button
                    BackgroundColor="#004f98"
                    Command="{Binding SaveCommand}"
                    FontFamily="Poppins"
                    FontSize="20"
                    Text="Next"
                    TextColor="#fff" />
                <HorizontalStackLayout HorizontalOptions="Center" Spacing="5">
                    <Label
                        FontFamily="MulishExtaBold"
                        FontSize="16"
                        Opacity="0.8"
                        Text="Already have an account?"
                        TextColor="#4E4E4E" />
                    <Label
                        FontAttributes="Bold"
                        FontFamily="MulishExtaBold"
                        FontSize="16"
                        Text=" Sign In"
                        TextColor="#004f98">
                        <Label.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding GoToSigninCommand}" />
                        </Label.GestureRecognizers>
                    </Label>
                </HorizontalStackLayout>
            </VerticalStackLayout>

        </VerticalStackLayout>
    </Border>
</local:SignupFormViewBase>
