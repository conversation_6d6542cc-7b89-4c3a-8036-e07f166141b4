@page "/profile"
@using Platform.Framework.Core
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject ILocalStorageService StorageService

<!-- Profile Management - WhatsApp Style -->
<div class="flex flex-col h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Compact Header -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div class="flex items-center justify-between">
            <h1 class="text-xl font-semibold text-gray-900 dark:text-white">Profile</h1>
            <button @onclick="SaveProfile" 
                    class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                Save
            </button>
        </div>
    </div>

    <!-- Profile Content -->
    <div class="flex-1 overflow-y-auto">
        <!-- Profile Picture Section -->
        <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-6">
            <div class="flex flex-col items-center">
                <!-- Avatar -->
                <div class="relative mb-4">
                    @if (!string.IsNullOrEmpty(profilePictureUrl))
                    {
                        <img src="@profilePictureUrl" alt="Profile Picture" 
                             class="w-32 h-32 rounded-full object-cover border-4 border-gray-200 dark:border-gray-600" />
                    }
                    else
                    {
                        <div class="w-32 h-32 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center border-4 border-gray-200 dark:border-gray-600">
                            <span class="text-4xl font-bold text-white">
                                @GetInitials(displayName)
                            </span>
                        </div>
                    }
                    <!-- Edit Button -->
                    <button @onclick="ChangeProfilePicture" 
                            class="absolute bottom-2 right-2 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-2 shadow-lg transition-colors duration-200">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                    </button>
                </div>
                
                <!-- Display Name -->
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-1">@displayName</h2>
                <p class="text-sm text-gray-500 dark:text-gray-400">@username</p>
            </div>
        </div>

        <!-- Profile Information -->
        <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 mt-4">
            <div class="px-4 py-3">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Profile Information</h3>
                
                <!-- Display Name -->
                <div class="mb-4">
                    <label for="displayName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Display Name
                    </label>
                    <div class="relative">
                        <input type="text" @bind="displayName" id="displayName" 
                               placeholder="Enter your display name"
                               class="block w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Username -->
                <div class="mb-4">
                    <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Username
                    </label>
                    <div class="relative">
                        <input type="text" @bind="username" id="username" 
                               placeholder="Enter your username"
                               class="block w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400"></span>
                        </div>
                    </div>
                </div>

                <!-- Status -->
                <div class="mb-4">
                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Status
                    </label>
                    <div class="relative">
                        <input type="text" @bind="status" id="status" 
                               placeholder="What's on your mind?"
                               maxlength="100"
                               class="block w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <span class="text-xs text-gray-400">@(status?.Length ?? 0)/100</span>
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        This will be visible to your friends
                    </p>
                </div>
            </div>
        </div>

        <!-- Account Information -->
        <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 mt-4">
            <div class="px-4 py-3">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Account Information</h3>
                
                <!-- Email -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Email Address
                    </label>
                    <div class="relative">
                        <input type="email" value="@email" readonly
                               class="block w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed" />
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                            </svg>
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        Email cannot be changed
                    </p>
                </div>

                <!-- Member Since -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Member Since
                    </label>
                    <div class="relative">
                        <input type="text" value="@memberSince" readonly
                               class="block w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed" />
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Privacy Settings -->
        <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 mt-4">
            <div class="px-4 py-3">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Privacy</h3>
                
                <!-- Profile Visibility -->
                <div class="flex items-center justify-between py-3">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Profile Visibility</h4>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Who can see your profile information</p>
                    </div>
                    <select @bind="profileVisibility" 
                            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="everyone">Everyone</option>
                        <option value="friends">Friends Only</option>
                        <option value="private">Private</option>
                    </select>
                </div>

                <!-- Last Seen -->
                <div class="flex items-center justify-between py-3">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Last Seen</h4>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Show when you were last online</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" @bind="showLastSeen" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="bg-white dark:bg-gray-800 mt-4">
            <div class="px-4 py-3">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Actions</h3>
                
                <!-- Change Password -->
                <div @onclick="ChangePassword" 
                     class="flex items-center py-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 -mx-4 px-4 rounded-lg transition-colors duration-200">
                    <div class="flex-shrink-0 mr-3">
                        <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-6 6h-2.5a2.5 2.5 0 01-2.5-2.5 2.5 2.5 0 012.5-2.5H16a2 2 0 002-2m-7 4v4m0 0H9m2 0h2m-2-4a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Change Password</h4>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Update your account password</p>
                    </div>
                    <svg class="w-5 h-5 text-gray-400 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>

                <!-- Delete Account -->
                <div @onclick="DeleteAccount" 
                     class="flex items-center py-3 cursor-pointer hover:bg-red-50 dark:hover:bg-red-900/20 -mx-4 px-4 rounded-lg transition-colors duration-200">
                    <div class="flex-shrink-0 mr-3">
                        <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-red-600 dark:text-red-400">Delete Account</h4>
                        <p class="text-sm text-red-500 dark:text-red-400">Permanently delete your account</p>
                    </div>
                    <svg class="w-5 h-5 text-red-400 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>
