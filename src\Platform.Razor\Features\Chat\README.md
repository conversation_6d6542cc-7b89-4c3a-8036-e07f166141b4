# Chat System Razor Components

A comprehensive chat system built with B<PERSON>zor and styled with Tailwind CSS. This system provides real-time messaging capabilities with modern UI/UX patterns and full hybrid app functionality.

## Components Overview

### 1. ChatThreadsListing Component
**Location**: `Platform.Razor/Features/Chat/Listing/ChatThreadsListing.razor`

A modern chat threads listing component that displays all user conversations with search, real-time updates, and responsive design.

#### Features
- **Real-time Search**: Debounced search with 500ms delay for optimal performance
- **Responsive Design**: Mobile-first approach with touch-friendly interface
- **Thread Cards**: Modern cards with participant avatars, last message preview, and timestamps
- **Unread Indicators**: Visual indicators for unread messages
- **Pagination**: Server-side pagination with navigation controls
- **Loading States**: Beautiful loading animations and error handling
- **Empty States**: Informative messages for no conversations

#### Usage
```razor
@page "/chat"
<ChatThreadsListing />
```

### 2. MessagesListing Component
**Location**: `Platform.Razor/Features/Chat/Messages/MessagesListing.razor`

A comprehensive messages display component with chat bubbles, auto-scroll, and status indicators.

#### Features
- **Message Bubbles**: Distinct styling for incoming and outgoing messages
- **Auto-scroll**: Automatic scrolling to bottom for new messages
- **Message Status**: Delivery status indicators (pending, sent, delivered, read)
- **Search Messages**: In-chat message search functionality
- **Timestamp Display**: Smart timestamp formatting (relative and absolute)
- **Responsive Layout**: Mobile-optimized chat interface
- **Scroll Pagination**: Load message history on scroll

#### Usage
```razor
@page "/chat/{ConversationId}"
<MessagesListing ConversationId="@ConversationId" />
```

### 3. MessageForm Component
**Location**: `Platform.Razor/Features/Chat/Messages/MessageForm.razor`

A rich message input component with emoji picker, file upload, and typing indicators.

#### Features
- **Rich Text Input**: Auto-resizing textarea with keyboard shortcuts
- **Emoji Picker**: Built-in emoji selection with common emojis
- **File Attachments**: Support for multiple file uploads with preview
- **Typing Indicators**: Real-time typing status
- **Send on Enter**: Keyboard shortcut for sending messages
- **File Validation**: File type and size validation
- **Error Handling**: Comprehensive error states and recovery

#### Usage
```razor
<MessageForm ConversationId="@ConversationId" OnMessageSent="OnMessageSent" />
```

## Architecture

### Component Inheritance
All components follow the established base class patterns:

- **ChatThreadsListing**: Inherits from `ListingBase<ChatThreadsListingViewModel, ChatThreadsListingBusinessObject, ChatThreadsFilterViewModel, ChatThreadsFilterBusinessObject, IChatThreadsListingDataService>`
- **MessagesListing**: Inherits from `ListingBase<ChatMessagesListingViewModel, ChatMessagesListingBusinessObject, ChatMessagesFilterViewModel, ChatMessagesFilterBusinessObject, IChatMessagesListingDataService>`
- **MessageForm**: Inherits from `FormBase<ChatMessageFormBusinessObject, ChatMessageFormViewModel, string, IChatMessageFormDataService>`

### Data Flow
1. **ViewModels**: Located in `Platform.Client.Services.Features.Conversation`
2. **Business Objects**: Located in `DeepMessage.ServiceContracts.Features.Conversation`
3. **Data Services**: Both server-side and client-side implementations
4. **Real-time Updates**: SignalR integration for live messaging

## File Structure
```
Platform.Razor/Features/Chat/
├── Listing/
│   ├── ChatThreadsListing.razor          # Chat threads listing component
│   └── ChatThreadsListing.razor.cs       # Component logic
├── Messages/
│   ├── MessagesListing.razor             # Messages display component
│   ├── MessagesListing.razor.cs          # Component logic
│   ├── MessageForm.razor                 # Message input component
│   └── MessageForm.razor.cs              # Form logic
├── Components/                           # Additional chat components
└── README.md                             # This documentation
```

## Features in Detail

### Real-time Messaging
- **SignalR Integration**: Real-time message delivery and read receipts
- **Typing Indicators**: Live typing status updates
- **Message Status**: Real-time delivery and read status updates
- **Auto-refresh**: Automatic updates when new messages arrive

### Search Functionality
- **Thread Search**: Search conversations by participant name or last message
- **Message Search**: In-chat message content search
- **Debounced Input**: 500ms delay prevents excessive API calls
- **Server-side Filtering**: Search processed on server for better performance

### File Handling
- **Multiple File Types**: Support for images, videos, documents
- **File Preview**: Visual preview of selected files before sending
- **Size Validation**: File size limits and validation
- **Drag & Drop**: Drag and drop file upload support

### Mobile Experience
- **Touch Gestures**: Swipe gestures for mobile navigation
- **Responsive Layout**: Optimized for all screen sizes
- **Virtual Keyboard**: Proper handling of mobile keyboards
- **Touch Targets**: Appropriate button sizes for touch interaction

## Styling

### Tailwind CSS Classes
The components use comprehensive Tailwind CSS classes for:
- **Layout**: Flexbox and grid layouts for responsive design
- **Colors**: Consistent color scheme with dark mode support
- **Typography**: Responsive text sizing and weights
- **Interactions**: Hover effects, focus states, smooth transitions
- **Animations**: Loading spinners, typing indicators, smooth scrolling

### Message Bubbles
- **Incoming Messages**: Light background with left alignment
- **Outgoing Messages**: Blue background with right alignment
- **Timestamps**: Subtle timestamps with smart formatting
- **Status Indicators**: Visual delivery status icons

## Integration

### Existing Services
The components integrate with existing services:
- `IChatThreadsListingDataService`: For fetching conversation threads
- `IChatMessagesListingDataService`: For fetching messages
- `IChatMessageFormDataService`: For sending messages
- Both server-side and client-side implementations supported

### Navigation
- **Chat Threads**: `/chat`
- **Conversation**: `/chat/{conversationId}`
- **New Chat**: `/chat/new`
- **Back Navigation**: Proper navigation stack management

### Real-time Features
- **SignalR Hubs**: Integration with chat hubs for real-time updates
- **Message Delivery**: Real-time message delivery notifications
- **Read Receipts**: Read status tracking and display
- **Typing Indicators**: Live typing status updates

## Performance Considerations

### Message Loading
- **Pagination**: Server-side pagination for message history
- **Lazy Loading**: Load messages on demand as user scrolls
- **Caching**: Client-side caching for better performance
- **Virtual Scrolling**: Efficient rendering of large message lists

### Search Optimization
- **Debouncing**: Prevents excessive API calls during typing
- **Server-side Search**: Reduces data transfer and client processing
- **Search Indexing**: Optimized database queries for search

### Memory Management
- **Component Disposal**: Proper cleanup of timers and event handlers
- **Event Unsubscription**: Clean up SignalR connections
- **File Cleanup**: Proper disposal of file references

## Browser Support
- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile Browsers**: iOS Safari, Chrome Mobile, Samsung Internet
- **JavaScript Required**: Components require JavaScript for full functionality
- **WebSocket Support**: Required for real-time features

## Accessibility

### WCAG Compliance
- **Keyboard Navigation**: Full keyboard support for all interactions
- **Screen Readers**: Proper ARIA labels and semantic HTML
- **Focus Management**: Clear focus indicators and logical tab order
- **Color Contrast**: WCAG AA compliant color combinations

### Chat-specific Accessibility
- **Message Announcements**: Screen reader announcements for new messages
- **Typing Indicators**: Accessible typing status announcements
- **File Descriptions**: Proper descriptions for file attachments
- **Timestamp Reading**: Accessible timestamp formatting

## Security Considerations

### Message Security
- **Input Sanitization**: XSS prevention for message content
- **File Validation**: Secure file upload validation
- **Authentication**: Proper user authentication for chat access
- **Authorization**: Message access control and permissions

### Data Protection
- **Encryption**: End-to-end encryption support (implementation dependent)
- **Data Retention**: Configurable message retention policies
- **Privacy Controls**: User privacy settings and controls

## Customization

### Styling Customization
1. **Tailwind Configuration**: Modify Tailwind config for global changes
2. **CSS Variables**: Use CSS custom properties for theme customization
3. **Component Overrides**: Override specific component styles

### Functionality Customization
1. **Message Types**: Extend support for different message types
2. **Emoji Sets**: Customize emoji picker with different emoji sets
3. **File Types**: Configure supported file types and sizes
4. **Search Behavior**: Customize search algorithms and filters

## Related Components

### XAML Versions
- **XAML Chat Threads**: `Platform.Client.Common/Features/Conversation/ChatThreads/`
- **XAML Messages**: `Platform.Client.Common/Features/Conversation/ChatMessages/`

### Base Classes
- **ListingBase**: `Platform.Razor/Base/ListingBase.cs`
- **FormBase**: `Platform.Razor/Base/FormBase.cs`

## Contributing

When making changes to these components:
1. **Follow Patterns**: Maintain consistency with established patterns
2. **Test Real-time**: Verify real-time functionality works correctly
3. **Mobile Testing**: Test on multiple screen sizes and devices
4. **Accessibility**: Ensure accessibility standards are maintained
5. **Performance**: Consider performance impact of changes
6. **Documentation**: Update this README for any new features

## Troubleshooting

### Common Issues
1. **Messages Not Loading**: Check SignalR connection and data service configuration
2. **Search Not Working**: Verify server-side search implementation
3. **File Upload Errors**: Check file size limits and type validation
4. **Styling Issues**: Verify Tailwind CSS is properly configured
5. **Real-time Issues**: Check SignalR hub configuration and connection

### Debug Tips
1. **Browser DevTools**: Use for CSS and JavaScript debugging
2. **Network Tab**: Monitor API calls and SignalR connections
3. **Console Logs**: Check for JavaScript errors and warnings
4. **Blazor DevTools**: Use Blazor-specific debugging tools
