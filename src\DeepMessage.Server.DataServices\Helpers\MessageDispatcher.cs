﻿using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using FirebaseAdmin.Messaging;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Threading.Channels;

namespace DeepMessage.Server.DataServices.Helpers;

public class MessageDispatcher
{
    private Channel<ChatMessagesSyncFormBusinessObject> ChatMessages { get; set; }

    private Channel<ChatMessageUpdate> ChatMessageUpdates { get; set; }

    private readonly Task? _dispatcherTask;
    //private readonly Task? _monitoringTask;
    private readonly IServiceScopeFactory scopeFactory;
    private readonly DeepChatHub chatHub;

    private ILogger<MessageDispatcher> _logger;

    public MessageDispatcher(IServiceScopeFactory scopeFactory, DeepChatHub chatHub, ILogger<MessageDispatcher> logger)
    {
        ChatMessages = Channel.CreateUnboundedPrioritized(
            new UnboundedPrioritizedChannelOptions<ChatMessagesSyncFormBusinessObject>()
            {
                Comparer = Comparer<ChatMessagesSyncFormBusinessObject>.Create((x, y) => x.Id.CompareTo(y.Id))
            });

        ChatMessageUpdates = Channel.CreateUnbounded<ChatMessageUpdate>();

        _dispatcherTask = Task.Factory.StartNew(async () =>
        {
            var fiveMinutesAgo = DateTime.UtcNow.AddMinutes(-5);
            while (true)
            {
                try
                {
                    var message = await ChatMessages.Reader.ReadAsync();
                    try
                    {
                        var scope = scopeFactory.CreateScope();
                        var newsService = scope.ServiceProvider.GetRequiredService<RssNewsHelper>();
                        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                        var memoryCache = scope.ServiceProvider.GetRequiredService<IMemoryCache>();

                        // ✅ SECURE: Query MessageRecipients for encrypted content delivery
                        var messageRecipients = context.MessageRecipients.Where(x => x.MessageId == message.Id
                                && x.DeliveryStatus < DeliveryStatus.DeliveredToEndUser
                                && x.RecipientId != message.SenderId).ToList();

                        foreach (var recipient in messageRecipients)
                        {
                            var userName = await memoryCache.GetOrCreateAsync($"username_{recipient.RecipientId}", async entry =>
                            {
                                var user = await context.Users.FirstOrDefaultAsync(x => x.Id == recipient.RecipientId);
                                return user.UserName;
                            }); // just for logging/debugging

                            var utcNow = DateTime.UtcNow;
                            // ✅ SECURE: Send encrypted content instead of plaintext
                            var result = await chatHub.SendMessageAsync(recipient.RecipientId, userName,
                            message.Id, recipient.EncryptedContent, JsonSerializer.Serialize(message), SignalRMethod.OnNewFeed);
                            if (result) // Fixed: was hardcoded to false
                            {
                                recipient.DeliveryStatus = DeliveryStatus.SentToEndUserViaSignalR;
                                await context.SaveChangesAsync();

                                await context.Messages.Where(x => x.Id == message.Id && x.DeliveryStatus < DeliveryStatus.SentToEndUserViaSignalR)
                                 .ExecuteUpdateAsync(x => x
                                 .SetProperty(p => p.DeliveryStatus, DeliveryStatus.SentToEndUserViaSignalR)
                                 .SetProperty(p => p.DeliveryStatusTime, utcNow));

                                DispatchMessageUpdate(new ChatMessageUpdate()
                                {
                                    Id = message.Id,
                                    SenderId = message.SenderId,
                                    DeliveryStatusTime = utcNow,
                                    DeliveryStatus = DeliveryStatus.SentToEndUserViaSignalR,
                                });

                                logger.LogInformation("Message {MessageId} delivered via SignalR to user {UserId}", message.Id, recipient.RecipientId);
                            }
                            else if (message.EnableFallBackChannel)
                            {
                                 var deviceToken = await memoryCache.GetOrCreateAsync($"devicetoken_{recipient.RecipientId}", async entry =>
                                {
                                    var temp = await context.UserDevices.Where(x => x.UserId == recipient.RecipientId)
                                    .Select(x => x.DeviceToken).FirstOrDefaultAsync();
                                    return temp;
                                });

                                // 2) Prepare the message with actual chat content
                                var fcmMessage = new FirebaseAdmin.Messaging.Message()
                                {
                                    Token = deviceToken,
                                    Notification = new Notification()
                                    {
                                        Title = $"New message from {userName}",
                                        Body = message.PlainContent ?? "New message received",
                                    },
                                    Data = new Dictionary<string, string>
                                    {
                                        ["messageId"] = message.Id,
                                        ["conversationId"] = message.ConversationId,
                                        ["senderId"] = message.SenderId,
                                        ["type"] = "chat_message"
                                    },
                                    Android = new AndroidConfig
                                    {
                                        Priority = Priority.High,
                                        TimeToLive = TimeSpan.FromHours(24),
                                        CollapseKey = $"chat_{message.ConversationId}",
                                        Notification = new AndroidNotification
                                        {
                                            ChannelId = "chat_messages",
                                            Sound = "default"
                                        }
                                    },
                                    Apns = new ApnsConfig
                                    {
                                        Aps = new Aps
                                        {
                                            Alert = new ApsAlert
                                            {
                                                Title = $"New message from {userName}",
                                                Body = message.PlainContent ?? "New message received"
                                            },
                                            Badge = 1,
                                            Sound = "default"
                                        }
                                    }
                                };

                                // 3) Send push notification with error handling
                                try
                                {
                                    if (!string.IsNullOrEmpty(deviceToken))
                                    {
                                        string response = await FirebaseMessaging.DefaultInstance.SendAsync(fcmMessage);
                                        logger.LogInformation("Push notification sent successfully. MessageId: {MessageId}, Response: {Response}", message.Id, response);

                                        recipient.DeliveryStatus = DeliveryStatus.SentToEndUserViaPushNotification;
                                        await context.SaveChangesAsync();
                                    }
                                    else
                                    {
                                        logger.LogWarning("No device token found for user {UserId}, message {MessageId}", recipient.RecipientId, message.Id);
                                        recipient.DeliveryStatus = DeliveryStatus.DeliveryFailed;
                                        await context.SaveChangesAsync();
                                    }
                                }
                                catch (Exception fcmEx)
                                {
                                    logger.LogError(fcmEx, "Failed to send push notification for message {MessageId} to user {UserId}", message.Id, recipient.RecipientId);
                                    recipient.DeliveryStatus = DeliveryStatus.DeliveryFailed;
                                    await context.SaveChangesAsync();
                                }

                                await context.Messages.Where(x => x.Id == message.Id && x.DeliveryStatus < DeliveryStatus.SentToEndUserViaPushNotification)
                               .ExecuteUpdateAsync(x => x
                               .SetProperty(p => p.DeliveryStatus, DeliveryStatus.SentToEndUserViaPushNotification)
                               .SetProperty(p => p.DeliveryStatusTime, utcNow));

                                DispatchMessageUpdate(new ChatMessageUpdate()
                                {
                                    Id = message.Id,
                                    SenderId = message.SenderId,
                                    DeliveryStatusTime = utcNow,
                                    DeliveryStatus = DeliveryStatus.SentToEndUserViaPushNotification,
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        DispatchMessage(message);
                        logger.LogWarning(ex.Message);
                    }
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex.Message);
                }
            }
        });

        Task.Factory.StartNew(async () =>
        {
            while (true)
            {
                try
                {
                    var message_ = await ChatMessageUpdates.Reader.ReadAsync();
                    var scope = scopeFactory.CreateScope();
                    var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                    var hybridCache = scope.ServiceProvider.GetRequiredService<HybridCache>();
                    var message = await context.Messages.Where(x => x.Id == message_.Id).FirstAsync();

                    var userName = await hybridCache.GetOrCreateAsync(message.SenderId, async entry =>
                    {
                        var user = await context.Users.FirstOrDefaultAsync(x => x.Id == message.SenderId);
                        return user.UserName;
                    });

                    var result = await chatHub.SendMessageAsync(message.SenderId, userName,
                        message.Id, message.DeliveryStatus.ToString(), JsonSerializer.Serialize(message_), SignalRMethod.OnFeedUpdate);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                }
            }
        });


        //_monitoringTask = Task.Factory.StartNew(async () =>
        // {
        //     while (true)
        //     {
        //         await Task.Delay(10000);
        //         var fiveMinutesAgo = DateTime.UtcNow.AddMinutes(-1);
        //         var scope = scopeFactory.CreateScope();
        //         var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        //         var pendingMessages = (from m in context.Messages
        //                                from recipients in context.MessageRecipients.Where(x => x.MessageId == m.Id)
        //                                from user in context.Users.Where(x => x.Id == recipients.RecipientId)
        //                                where
        //                                //m.Id == "0195ffba-4ae1-7a10-8dc2-84f438f42825" &&
        //                                recipients.DeliveryStatus == DeliveryStatus.Pending ||
        //                                (recipients.DeliveryStatus == DeliveryStatus.QueuedToUpSync && recipients.DeliveryStatusTime < fiveMinutesAgo) ||
        //                                 (recipients.DeliveryStatus == DeliveryStatus.SentToEndUserViaSignalR && recipients.DeliveryStatusTime < fiveMinutesAgo)
        //                                orderby m.Id
        //                                select new ChatMessagesSyncFormBusinessObject
        //                                {
        //                                    Id = m.Id,
        //                                    MessageRecepientId = recipients.Id,
        //                                    MessageRecepientUserName = user.UserName,
        //                                    ConversationId = m.ConversationId,
        //                                    SenderId = m.SenderId,
        //                                    CreatedAt = m.CreatedAt,
        //                                    DeletedAt = m.DeletedAt,
        //                                    DisappearAfter = m.DisappearAfter,
        //                                    DisappearAt = m.DisappearAt,
        //                                    EditedAt = m.EditedAt,
        //                                    IsDeleted = m.IsDeleted,
        //                                    IsEdited = m.IsEdited,
        //                                    IsEphemeral = m.IsEphemeral,
        //                                    PlainContent = m.PlainContent,
        //                                }).ToList();
        //         pendingMessages = pendingMessages.DistinctBy(x => x.Id).ToList();
        //         foreach (var item in pendingMessages)
        //         {
        //             DispatchMessage(item);
        //         }

        //         var recipientIds = pendingMessages.Select(x => x.MessageRecepientId).ToList();
        //         await context.MessageRecipients
        //               .Where(x => recipientIds.Contains(x.Id))
        //               .ExecuteUpdateAsync(x => x.SetProperty(p => p.DeliveryStatus, DeliveryStatus.QueuedToUpSync)
        //               .SetProperty(p => p.DeliveryStatusTime, DateTime.UtcNow));
        //     }
        // });

        this.scopeFactory = scopeFactory;
        this.chatHub = chatHub;
        _logger = logger;
    }


    public bool DispatchMessage(ChatMessagesSyncFormBusinessObject chatMessage)
    {
        return ChatMessages.Writer.TryWrite(chatMessage);
    }

    public bool DispatchMessageUpdate(ChatMessageUpdate chatMessageUpdate)
    {
        return ChatMessageUpdates.Writer.TryWrite(chatMessageUpdate);
    }
}