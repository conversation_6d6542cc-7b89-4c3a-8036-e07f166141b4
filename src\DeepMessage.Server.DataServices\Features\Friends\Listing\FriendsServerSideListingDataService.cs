﻿using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
namespace DeepMessage.Server.DataServices.Features.Friends;

public class FriendsServerSideListingDataService : 
        ServerSideListingDataService<FriendsListingBusinessObject, FriendsFilterBusinessObject>, IFriendsListingDataService
{

    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public FriendsServerSideListingDataService(AppDbContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
	public override IQueryable<FriendsListingBusinessObject> GetQuery(FriendsFilterBusinessObject filterBusinessObject)
	{
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        var query = (from f in _context.Friendships
                where f.UserId == userId
                select new FriendsListingBusinessObject
                {
                    Id = f.Id,
                    FriendId = f.FriendId,
                    Avatar = f.DisplayPictureUrl,
                    Name = f.Name,
                    Status = "Online" // You can enhance this with actual status logic
                });

        // Apply search filter if SearchKey is provided
        if (!string.IsNullOrWhiteSpace(filterBusinessObject.SearchKey))
        {
            var searchTerm = filterBusinessObject.SearchKey.ToLower();
            query = query.Where(friend =>
                friend.Name.ToLower().Contains(searchTerm)
            );
        }

        // Order by name for consistent results
        return query.OrderBy(friend => friend.Name);
    }
}
