using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Platform.Razor.Features.Profile.Management
{
    public partial class ProfileManagement
    {
     
        // Profile Information
        private string displayName = "<PERSON>";
        private string username = "johndo<PERSON>";
        private string status = "Available";
        private string email = "<EMAIL>";
        private string memberSince = "January 2024";
        private string profilePictureUrl = "";

        // Privacy Settings
        private string profileVisibility = "friends";
        private bool showLastSeen = true;

        protected override async Task OnInitializedAsync()
        {
            await LoadProfile();
        }

        /// <summary>
        /// Loads profile data from storage
        /// </summary>
        private async Task LoadProfile()
        {
            try
            {
                // Load profile information
                var storedDisplayName = await StorageService.GetValue("display_name");
                if (!string.IsNullOrEmpty(storedDisplayName))
                {
                    displayName = storedDisplayName;
                }

                var storedUsername = await StorageService.GetValue("username");
                if (!string.IsNullOrEmpty(storedUsername))
                {
                    username = storedUsername;
                }

                var storedStatus = await StorageService.GetValue("user_status");
                if (!string.IsNullOrEmpty(storedStatus))
                {
                    status = storedStatus;
                }

                var storedEmail = await StorageService.GetValue("user_email");
                if (!string.IsNullOrEmpty(storedEmail))
                {
                    email = storedEmail;
                }

                var storedProfilePicture = await StorageService.GetValue("profile_picture_url");
                if (!string.IsNullOrEmpty(storedProfilePicture))
                {
                    profilePictureUrl = storedProfilePicture;
                }

                // Load privacy settings
                var storedVisibility = await StorageService.GetValue("profile_visibility");
                if (!string.IsNullOrEmpty(storedVisibility))
                {
                    profileVisibility = storedVisibility;
                }

                var storedLastSeen = await StorageService.GetValue("show_last_seen");
                if (!string.IsNullOrEmpty(storedLastSeen))
                {
                    showLastSeen = bool.Parse(storedLastSeen);
                }
            }
            catch (Exception)
            {
                // Use default values if loading fails
            }
        }

        /// <summary>
        /// Saves profile data to storage
        /// </summary>
        private async Task SaveProfile()
        {
            try
            {
                // Save profile information
                await StorageService.SetValue(displayName, "display_name");
                await StorageService.SetValue(username, "username");
                await StorageService.SetValue(status, "user_status");
                await StorageService.SetValue(profilePictureUrl, "profile_picture_url");

                // Save privacy settings
                await StorageService.SetValue(profileVisibility, "profile_visibility");
                await StorageService.SetValue(showLastSeen.ToString(), "show_last_seen");

                // Show success message
                await ShowToast("Profile saved successfully");
            }
            catch (Exception)
            {
                await ShowToast("Error saving profile");
            }
        }

        /// <summary>
        /// Changes profile picture
        /// </summary>
        private async Task ChangeProfilePicture()
        {
            await ShowToast("Profile picture change coming soon");
        }

        /// <summary>
        /// Changes password
        /// </summary>
        private async Task ChangePassword()
        {
            await ShowToast("Password change coming soon");
        }

        /// <summary>
        /// Navigates to referral codes page
        /// </summary>
        private void NavigateToReferralCodes()
        {
            Navigation.NavigateTo("/referral-codes");
        }

        /// <summary>
        /// Deletes account
        /// </summary>
        private async Task DeleteAccount()
        {
            await ShowToast("Account deletion coming soon");
        }

        /// <summary>
        /// Gets initials from display name
        /// </summary>
        private string GetInitials(string name)
        {
            if (string.IsNullOrEmpty(name))
                return "?";

            var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length >= 2)
                return $"{parts[0][0]}{parts[1][0]}".ToUpper();
            
            return name[0].ToString().ToUpper();
        }

        /// <summary>
        /// Shows a toast message
        /// </summary>
        private async Task ShowToast(string message)
        {
            try
            {
                await JSRuntime.InvokeVoidAsync("eval", $@"
                    const toast = document.createElement('div');
                    toast.className = 'fixed top-4 right-4 bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg z-50';
                    toast.textContent = '{message}';
                    document.body.appendChild(toast);
                    setTimeout(() => {{
                        toast.remove();
                    }}, 3000);
                ");
            }
            catch
            {
                // Ignore errors
            }
        }
    }
}
