﻿@using Platform.Framework.Core
@inherits FrameworkLayoutBaseComponent

<!-- Empty Layout - WhatsApp Minimalistic Style with Nothing Phone Colors -->
<div class="flex flex-col h-screen bg-nothing-black-50 dark:bg-nothing-black-950 text-nothing-black-900 dark:text-white">
    <!-- Main Content Area -->
    <div class="flex-1 overflow-hidden">
        @Body
    </div>
    @if (DialogService != null)
    {
        foreach (var dialog in DialogService.Dialogs)
        {
            <div class="relative z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true">

                <div class="fixed inset-0 bg-gray-500/75 transition-opacity" aria-hidden="true"></div>

                <div class="z-[150] fixed inset-0 w-screen overflow-y-auto @dialog.DialogContainerClasses">
                    <div class="w-[calc(100%-0.5rem)] @dialog.PositionClasses m-1 flex @dialog.SizeClasses">
                        <div class="w-full overflow-y-auto overflow-x-hidden rounded-lg bg-white shadow-lg">

                            <div class="sticky top-0 z-40 flex items-center justify-between rounded-t border-b bg-white p-2 md:p-4">
                                <h3 class="truncate text-sm font-semibold text-gray-900 md:text-xl">
                                    @dialog.Title
                                </h3>
                                @if (dialog.ShowCrossIcon)
                                {
                                    <button @onclick='() => CloseMe(dialog)' type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center">
                                        <i class="fa-solid fa-xmark text-lg text-gray-900"></i>
                                        <span class="sr-only">Close modal</span>
                                    </button>
                                }

                            </div>
                            <div class="w-full">
                                <DynamicComponent Type="dialog.Component" Parameters="dialog.Parameters"></DynamicComponent>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
</div>

                                        