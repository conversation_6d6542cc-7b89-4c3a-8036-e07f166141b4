﻿@using Platform.Framework.Core
@inherits FrameworkLayoutBaseComponent

<!-- Main Layout - WhatsApp Minimalistic Style with Nothing Phone Colors -->
<div class="flex flex-col h-screen bg-nothing-black-50 dark:bg-nothing-black-950 text-nothing-black-900 dark:text-white">
    <!-- Main Content Area -->
    <div class="flex-1 overflow-hidden">
        @Body
    </div>
    @if (DialogService != null)
    {
        @foreach (var dialog in DialogService.Dialogs)
        {
            <div class="relative z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true">
                <!-- Backdrop -->
                <div class="fixed inset-0 bg-gray-600/50 transition-opacity" aria-hidden="true"></div>

                <!-- Modal Container -->
                <div class="fixed inset-0 z-[150] flex items-center justify-center p-4 sm:p-6 @dialog.DialogContainerClasses">
                    <!-- Modal Content Wrapper -->
                    <div class="w-full max-w-lg overflow-hidden rounded-lg bg-white shadow-lg @dialog.SizeClasses">
                        <!-- Modal Header -->
                        <div class="flex items-center justify-between border-b bg-white p-3 sm:p-4">
                            <h3 class="truncate text-sm font-semibold text-gray-900 sm:text-xl">
                                @dialog.Title
                            </h3>
                            @if (dialog.ShowCrossIcon)
                            {
                                <button @onclick='() => CloseMe(dialog)' type="button"
                                        class="ml-auto inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900">
                                    <img src="images/xmark_solid.svg" class="h-4 w-4"  />
                                    <span class="sr-only">Close modal</span>
                                </button>
                            }
                        </div>

                        <!-- Modal Body -->
                        <div class="max-h-[80vh] overflow-y-auto">
                            <DynamicComponent Type="dialog.Component" Parameters="dialog.Parameters" />
                        </div>
                    </div>
                </div>
            </div>
        }

    }
    <!-- Bottom Tab Navigation -->
    <Platform.Razor.Features.Navigation.BottomTabs.BottomTabNavigation />
</div>
 