﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Account;
namespace DeepMessage.Server.WebApis.Controller.Account;
[ApiController, Route("api/[controller]/[action]")]
public class SigninFormController : ControllerBase, ISignInFormDataService
{

	private readonly ISignInFormDataService dataService;
	
	public SigninFormController(ISignInFormDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] SigninFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<SigninFormBusinessObject> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
