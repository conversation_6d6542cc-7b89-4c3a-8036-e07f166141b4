﻿using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Enums;
namespace DeepMessage.ServiceContracts.Features.Conversation;
public class ChatMessagesListingBusinessObject
{
    public string Id { get; set; } = null!;

    public string? Content { get; set; }
    public DateTime? Timestamp { get; set; }
    public bool IsIncoming { get; set; }

    public DeliveryStatus DeliveryStatus { get; set; }
}
