﻿namespace Platform.Client.Services.Features.Conversation;
public class ChatThreadsListingViewModel
{
    public string Id { get; set; } = null!;

    public string Avatar { get; set; } = null!;

    public string Name { get; set; } = null!;

    public string? LastMessage { get; set; }

    public DateTime LastMessageTime { get; set; }

    public string LastMessageTimeString
    {
        get
        {
            var today = DateTime.UtcNow.Date;
            if (LastMessageTime.Date == today)
                return LastMessageTime.ToString("hh:mm tt");
            else if (LastMessageTime.Date == today.AddDays(-1))
                return "Yesterday";
            else
                return LastMessageTime.ToString("dd/MM/yyyy");
        }
    }
}
